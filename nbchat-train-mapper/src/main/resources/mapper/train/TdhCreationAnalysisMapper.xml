<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tydic.nbchat.train.mapper.TdhCreationAnalysisMapper">
    <resultMap type="com.tydic.nbchat.train.mapper.po.TdhCreationAnalysis" id="TdhCreationAnalysisMap">
        <result property="taskId" column="task_id" jdbcType="VARCHAR"/>
        <result property="videoClass" column="video_class" jdbcType="VARCHAR"/>
        <result property="sceneCategory" column="scene_category" jdbcType="VARCHAR"/>
        <result property="sceneCategorySub" column="scene_category_sub" jdbcType="VARCHAR"/>
        <result property="contentCategory" column="content_category" jdbcType="VARCHAR"/>
        <result property="contentCategorySub" column="content_category_sub" jdbcType="VARCHAR"/>
        <result property="vipType" column="vip_type" jdbcType="VARCHAR"/>
        <result property="vipTypeSub" column="vip_type_sub" jdbcType="VARCHAR"/>
        <result property="vipUseDays" column="vip_use_days" jdbcType="INTEGER"/>
        <result property="registerDays" column="register_days" jdbcType="INTEGER"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="analysisType" column="analysis_type" jdbcType="VARCHAR"/>
        <result property="resolution" column="resolution" jdbcType="VARCHAR"/>
        <result property="quality" column="quality" jdbcType="INTEGER"/>
        <result property="fps" column="fps" jdbcType="INTEGER"/>
        <result property="customVoice" column="custom_voice" jdbcType="VARCHAR"/>
        <result property="customTdh" column="custom_tdh" jdbcType="VARCHAR"/>
        <result property="subtitleEnable" column="subtitle_enable" jdbcType="INTEGER"/>
        <result property="subtitleBilingual" column="subtitle_bilingual" jdbcType="INTEGER"/>
        <result property="subtitleMark" column="subtitle_mark" jdbcType="INTEGER"/>
        <result property="banshuMark" column="banshu_mark" jdbcType="INTEGER"/>
        <result property="videoBar" column="video_bar" jdbcType="INTEGER"/>
        <result property="opId" column="op_id" jdbcType="VARCHAR"/>
        <result property="edId" column="ed_id" jdbcType="VARCHAR"/>
        <result property="pipUpload" column="pip_upload" jdbcType="INTEGER"/>
        <result property="tdhCrop" column="tdh_crop" jdbcType="INTEGER"/>
        <result property="pagMap" column="pag_map" jdbcType="INTEGER"/>
    </resultMap>

    <!-- 基本字段 -->
    <sql id="Base_Column_List">
        task_id, video_class, scene_category, scene_category_sub, content_category, content_category_sub, vip_type, vip_type_sub,
       vip_use_days, register_days, create_time, update_time, analysis_type, resolution, quality, fps, custom_voice,
       custom_tdh, subtitle_enable, subtitle_bilingual, subtitle_mark, banshu_mark, video_bar, op_id, ed_id, pip_upload,
       tdh_crop, pag_map</sql>

    <!--查询单个-->
    <select id="queryById" resultMap="TdhCreationAnalysisMap">
        select
        <include refid="Base_Column_List"/>
        from tdh_creation_analysis
        where task_id = #{taskId}
    </select>


    <select id="selectAll" resultMap="TdhCreationAnalysisMap" parameterType="com.tydic.nbchat.train.mapper.po.TdhCreationAnalysis">
        select
        <include refid="Base_Column_List" />
        from tdh_creation_analysis
        <where>
            <if test="taskId != null and taskId != ''">
                and task_id = #{taskId}
            </if>
            <if test="videoClass != null and videoClass != ''">
                and video_class = #{videoClass}
            </if>
            <if test="sceneCategory != null and sceneCategory != ''">
                and scene_category = #{sceneCategory}
            </if>
            <if test="sceneCategorySub != null and sceneCategorySub != ''">
                and scene_category_sub = #{sceneCategorySub}
            </if>
            <if test="contentCategory != null and contentCategory != ''">
                and content_category = #{contentCategory}
            </if>
            <if test="contentCategorySub != null and contentCategorySub != ''">
                and content_category_sub = #{contentCategorySub}
            </if>
            <if test="vipType != null and vipType != ''">
                and vip_type = #{vipType}
            </if>
            <if test="vipTypeSub != null and vipTypeSub != ''">
                and vip_type_sub = #{vipTypeSub}
            </if>
            <if test="vipUseDays != null">
                and vip_use_days = #{vipUseDays}
            </if>
            <if test="registerDays != null">
                and register_days = #{registerDays}
            </if>
            <if test="createTime != null">
                and create_time = #{createTime}
            </if>
            <if test="updateTime != null">
                and update_time = #{updateTime}
            </if>
            <if test="analysisType != null and analysisType != ''">
                and analysis_type = #{analysisType}
            </if>
            <if test="resolution != null and resolution != ''">
                and resolution = #{resolution}
            </if>
            <if test="quality != null">
                and quality = #{quality}
            </if>
            <if test="fps != null">
                and fps = #{fps}
            </if>
            <if test="customVoice != null and customVoice != ''">
                and custom_voice = #{customVoice}
            </if>
            <if test="customTdh != null and customTdh != ''">
                and custom_tdh = #{customTdh}
            </if>
            <if test="subtitleEnable != null">
                and subtitle_enable = #{subtitleEnable}
            </if>
            <if test="subtitleBilingual != null">
                and subtitle_bilingual = #{subtitleBilingual}
            </if>
            <if test="subtitleMark != null">
                and subtitle_mark = #{subtitleMark}
            </if>
            <if test="banshuMark != null">
                and banshu_mark = #{banshuMark}
            </if>
            <if test="videoBar != null">
                and video_bar = #{videoBar}
            </if>
            <if test="opId != null and opId != ''">
                and op_id = #{opId}
            </if>
            <if test="edId != null and edId != ''">
                and ed_id = #{edId}
            </if>
            <if test="pipUpload != null">
                and pip_upload = #{pipUpload}
            </if>
            <if test="tdhCrop != null">
                and tdh_crop = #{tdhCrop}
            </if>
            <if test="pagMap != null">
                and pag_map = #{pagMap}
            </if>
        </where>
    </select>



    <insert id="insertSelective" keyProperty="taskId" useGeneratedKeys="true" parameterType="com.tydic.nbchat.train.mapper.po.TdhCreationAnalysis">
        insert into tdh_creation_analysis
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="taskId != null and taskId != ''">
                task_id,
            </if>
            <if test="videoClass != null and videoClass != ''">
                video_class,
            </if>
            <if test="sceneCategory != null and sceneCategory != ''">
                scene_category,
            </if>
            <if test="sceneCategorySub != null and sceneCategorySub != ''">
                scene_category_sub,
            </if>
            <if test="contentCategory != null and contentCategory != ''">
                content_category,
            </if>
            <if test="contentCategorySub != null and contentCategorySub != ''">
                content_category_sub,
            </if>
            <if test="vipType != null and vipType != ''">
                vip_type,
            </if>
            <if test="vipTypeSub != null and vipTypeSub != ''">
                vip_type_sub,
            </if>
            <if test="vipUseDays != null">
                vip_use_days,
            </if>
            <if test="registerDays != null">
                register_days,
            </if>
            <if test="analysisType != null and analysisType != ''">
                analysis_type,
            </if>
            create_time
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="taskId != null and taskId != ''">
                #{taskId},
            </if>
            <if test="videoClass != null and videoClass != ''">
                #{videoClass},
            </if>
            <if test="sceneCategory != null and sceneCategory != ''">
                #{sceneCategory},
            </if>
            <if test="sceneCategorySub != null and sceneCategorySub != ''">
                #{sceneCategorySub},
            </if>
            <if test="contentCategory != null and contentCategory != ''">
                #{contentCategory},
            </if>
            <if test="contentCategorySub != null and contentCategorySub != ''">
                #{contentCategorySub},
            </if>
            <if test="vipType != null and vipType != ''">
                #{vipType},
            </if>
            <if test="vipTypeSub != null and vipTypeSub != ''">
                #{vipTypeSub},
            </if>
            <if test="vipUseDays != null">
                #{vipUseDays},
            </if>
            <if test="registerDays != null">
                #{registerDays},
            </if>
            <if test="analysisType != null and analysisType != ''">
                #{analysisType},
            </if>
            current_timestamp
        </trim>
    </insert>


    <!--新增所有列-->
    <insert id="insert" keyProperty="taskId" useGeneratedKeys="true">
        insert into tdh_creation_analysis(task_id, video_class, scene_category, scene_category_sub, content_category, content_category_sub, vip_type, vip_type_sub, vip_use_days, register_days,
        create_time, update_time, analysis_type)
        values (#{taskId}, #{videoClass}, #{sceneCategory}, #{sceneCategorySub}, #{contentCategory}, #{contentCategorySub}, #{vipType}, #{vipTypeSub}, #{vipUseDays}, #{registerDays}, #{createTime},
        #{updateTime}, #{analysisType})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update tdh_creation_analysis
        <set>
            <if test="videoClass != null and videoClass != ''">
                video_class = #{videoClass},
            </if>
            <if test="sceneCategory != null">
                scene_category = #{sceneCategory},
            </if>
            <if test="sceneCategorySub != null">
                scene_category_sub = #{sceneCategorySub},
            </if>
            <if test="contentCategory != null">
                content_category = #{contentCategory},
            </if>
            <if test="contentCategorySub != null">
                content_category_sub = #{contentCategorySub},
            </if>
            <if test="vipType != null and vipType != ''">
                vip_type = #{vipType},
            </if>
            <if test="vipTypeSub != null and vipTypeSub != ''">
                vip_type_sub = #{vipTypeSub},
            </if>
            <if test="vipUseDays != null">
                vip_use_days = #{vipUseDays},
            </if>
            <if test="registerDays != null">
                register_days = #{registerDays},
            </if>
            <if test="analysisType != null and analysisType != ''">
                analysis_type = #{analysisType},
            </if>
            update_time = current_timestamp
        </set>
        where task_id = #{taskId}
    </update>

    <!--通过主键删除-->
    <delete id="deleteById">
        delete
        from tdh_creation_analysis
        where task_id = #{taskId}
    </delete>
    <select id="queryDefaultByTaskId" resultMap="TdhCreationAnalysisMap">
        WITH ranked_data AS (SELECT tct.task_id,
        tct.user_id,
        tct.start_time,
        nuvl.vip_type,
        nuvl.valid_days,
        nuvl.start_time AS vip_start,
        row_number() OVER (PARTITION BY task_id ORDER BY vip_type DESC, tct.start_time) AS row_num
        FROM tdh_creation_task tct
        LEFT JOIN nbchat_user_vip_log nuvl
        ON tct.tenant_code = nuvl.tenant_code
        AND tct.user_id = nuvl.user_id
        AND tct.start_time BETWEEN nuvl.start_time AND nuvl.end_time
        WHERE tct.user_id != '1'
        AND tct.task_id = #{taskId,jdbcType=VARCHAR})
        SELECT rd.task_id,
        rd.vip_type,
        CASE
        WHEN rd.valid_days >= 365 THEN '3'
        WHEN rd.valid_days >= 93 THEN '2'
        WHEN rd.valid_days >= 31 THEN '1'
        ELSE '0' END AS vip_type_sub,
        datediff(rd.start_time, rd.vip_start) AS vip_use_days,
        datediff(rd.start_time, nu.created_time) AS register_days,
        '0' AS analysis_type
        FROM ranked_data rd
        LEFT JOIN (SELECT user_id,
        created_time
        FROM nbchat_user
        WHERE is_deleted = FALSE) nu
        ON rd.user_id = nu.user_id
        WHERE row_num = 1
    </select>
    <select id="selectAllForManualRefresh" resultMap="TdhCreationAnalysisMap">
        select
        <include refid="Base_Column_List"/>
        from tdh_creation_analysis
        where video_class is not null
        and (scene_category = '' or content_category = '')
        limit 200 offset #{offset}
    </select>
</mapper>
