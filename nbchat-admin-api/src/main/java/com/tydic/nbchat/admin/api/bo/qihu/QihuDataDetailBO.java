package com.tydic.nbchat.admin.api.bo.qihu;

import lombok.Data;

import java.io.Serializable;

@Data
public class QihuDataDetailBO implements Serializable {
    
    /**
     * 360点击ID (用于PC搜索推广和移动搜索)
     */
    private String qhclickid;
    
    /**
     * 展示ID (用于移动推广)
     */
    private String impressionId;
    
    /**
     * 精准匹配参数 (用于展示广告)
     */
    private String jzqs;
    
    /**
     * 事务ID
     */
    private String transId;
    
    /**
     * 事件类型
     * SUBMIT: 提交事件
     * ORDER: 订单事件
     */
    private String event;
    
    /**
     * 事件时间戳
     */
    private Long eventTime;
}
