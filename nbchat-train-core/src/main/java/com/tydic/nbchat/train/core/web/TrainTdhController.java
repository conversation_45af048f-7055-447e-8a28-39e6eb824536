package com.tydic.nbchat.train.core.web;

import com.tydic.nbchat.train.api.bo.tdh.*;
import com.tydic.nbchat.train.api.tdh.*;
import com.tydic.nbchat.user.api.bo.constants.UserAttributeConstants;
import com.tydic.nicc.common.bo.premission.Logical;
import com.tydic.nicc.common.bo.premission.RequiresRole;
import com.tydic.nicc.common.eums.EntityValidType;
import com.tydic.nicc.common.nbchat.constants.UserRoleConstants;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.base.bo.RspList;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


@Slf4j
@RestController
@RequestMapping("/train/tdh")
public class TrainTdhController {
    private final TdhBackgroundApi tdhBackgroundApi;
    private final TdhForegroundApi tdhForegroundApi;
    private final TdhVirtualHumanApi tdhVirtualHumanApi;
    private final TdhTemplateApi tdhTemplateApi;
    private final TdhVirtualAnchorApi tdhVirtualAnchorApi;
    private final TdhCreationRecordApi tdhCreationRecordApi;
    private final TdhCreationTaskApi tdhCreationTaskApi;

    private final TdhAudioApi tdhAudioApi;
    public TrainTdhController(TdhBackgroundApi tdhBackgroundApi,
                              TdhForegroundApi tdhForegroundApi,
                              TdhVirtualHumanApi tdhVirtualHumanApi,
                              TdhTemplateApi tdhTemplateApi,
                              TdhVirtualAnchorApi tdhVirtualAnchorApi,
                              TdhCreationRecordApi tdhCreationRecordApi,
                              TdhCreationTaskApi tdhCreationTaskApi, TdhAudioApi tdhAudioApi) {
        this.tdhBackgroundApi = tdhBackgroundApi;
        this.tdhForegroundApi = tdhForegroundApi;
        this.tdhVirtualHumanApi = tdhVirtualHumanApi;
        this.tdhTemplateApi = tdhTemplateApi;
        this.tdhVirtualAnchorApi = tdhVirtualAnchorApi;
        this.tdhCreationRecordApi = tdhCreationRecordApi;
        this.tdhCreationTaskApi = tdhCreationTaskApi;
        this.tdhAudioApi = tdhAudioApi;
    }

    @PostMapping("/background/list")
    public RspList getBackgroundList(@RequestBody TdhBackgroundQueryReqBO request){
       return tdhBackgroundApi.getBackgroundList(request);
    }

    @PostMapping("/background/save")
    public RspList saveBackground(@RequestBody TdhBackgroundQueryReqBO request){
        return tdhBackgroundApi.save(request);
    }

    @PostMapping("/foreground/list")
    public RspList getBackgroundList(@RequestBody TdhForegroundQueryReqBO request){
        return tdhForegroundApi.getForegroundList(request);
    }
    @PostMapping("/foreground/save")
    public RspList saveBackground(@RequestBody TdhForegroundQueryReqBO request){
        return tdhForegroundApi.save(request);
    }
    @RequiresRole(value = {UserRoleConstants.sysAdmin,
            UserRoleConstants.tenantAdmin}, logical = Logical.OR)
    @PostMapping("/foreground/admin_list")
    public RspList getBackgroundAdminList(@RequestBody TdhForegroundQueryReqBO request){
        return tdhForegroundApi.getForegroundAdminList(request);
    }
    @RequiresRole(value = {UserRoleConstants.sysAdmin,
            UserRoleConstants.tenantAdmin}, logical = Logical.OR)
    @PostMapping("/foreground/admin_save")
    public RspList saveBackgroundAdmin(@RequestBody TdhForegroundAdminReqBO request){
        return tdhForegroundApi.saveAdmin(request);
    }
    @PostMapping("/foreground/sort")
    @RequiresRole(value = {UserRoleConstants.sysAdmin,
            UserRoleConstants.tenantAdmin}, logical = Logical.OR)
    public Rsp sortForeground(@RequestBody TdhForegroundSortReqBO request){
        return tdhForegroundApi.sort(request);
    }
    @PostMapping("/foreground/query")
    public Rsp getForeground(@RequestBody TdhForegroundQueryReqBO request){
        return tdhForegroundApi.getForeground(request);
    }
    @PostMapping("/template/list")
    public RspList getTemplateList(@RequestBody TdhTemplateQueryReqBO request){
        return tdhTemplateApi.getTemplateList(request);
    }

    @PostMapping("/template/star/list")
    public RspList getStarTemplateList(@RequestBody TdhTemplateQueryReqBO request){
        return tdhTemplateApi.getStarTemplateList(request);
    }


    @RequiresRole(value = {UserRoleConstants.sysAdmin,
            UserRoleConstants.tenantAdmin}, logical = Logical.OR)
    @PostMapping("/template/admin_list")
    public RspList getTemplateAdminList(@RequestBody TdhTemplateQueryReqBO request){
        return tdhTemplateApi.getTemplateAdminList(request);
    }

    //首页热门模板，添加到白名单了
    @PostMapping("/template/hot")
    public RspList getHotTemplateList(@RequestBody TdhTemplateQueryReqBO request){
        return tdhTemplateApi.getHotTemplateList(request);
    }

    @PostMapping("/template/query")
    public Rsp getTemplate(@RequestBody TdhTemplateQueryReqBO request){
        return tdhTemplateApi.getTemplate(request);
    }

    @PostMapping("/template/save")
    public Rsp saveTemplate(@RequestBody TdhTemplateQueryReqBO request){
        return tdhTemplateApi.save(request);
    }

    @PostMapping("/human/list")
    public RspList getVirtualHumanList(@RequestBody TdhVirtualHumanQueryReqBO request){
        return tdhVirtualHumanApi.getVirtualHumanList(request);
    }

    @PostMapping("/human/update")
    @RequiresRole(value = {UserRoleConstants.sysAdmin})
    public Rsp saveOrUpdateVirtualHuman(@RequestBody TdhVirtualHumanUpdateReqBO request){
        return tdhVirtualHumanApi.saveOrUpdateVirtualHuman(request);
    }

    @PostMapping("/human/sort")
    @RequiresRole(value = {UserRoleConstants.sysAdmin})
    public Rsp sortVirtualHuman(@RequestBody TdhVirtualHumanSortReqBO request){
        return tdhVirtualHumanApi.sort(request);
    }

    /**
     * 数字人模版上下架
     * @param request
     * @return
     */
    @PostMapping("/human/save")
    @RequiresRole(value = {UserRoleConstants.sysAdmin})
    public Rsp saveVirtualHuman(@RequestBody TdhVirtualHumanUpdateReqBO request){
        return tdhVirtualHumanApi.updateStatus(request);
    }

    /**
     * 数字人模版删除
     * @param request
     * @return
     */
    @PostMapping("/human/delete")
    @RequiresRole(value = {UserRoleConstants.sysAdmin})
    public Rsp deleteVirtualHuman(@RequestBody TdhVirtualHumanUpdateReqBO request){
        return tdhVirtualHumanApi.delete(request);
    }

    /**
     * 运营平台数字人列表查询
     * @param request
     * @return
     */
    @PostMapping("/human/query")
    public RspList getQueryVirtualHumanList(@RequestBody TdhVirtualHumanQueryReqBO request){
        return tdhVirtualHumanApi.getQueryVirtualHumanList(request);
    }

    /**
     * 用户端数字人专区查询接口
     * @param request
     * @return
     */
    @PostMapping("/human/user/list")
    public RspList getUserVirtualHumanList(@RequestBody TdhVirtualHumanQueryReqBO request){
        return tdhVirtualHumanApi.getUserVirtualHumanList(request);
    }

    @PostMapping("/anchor/list")
    public RspList getVirtualAnchorList(@RequestBody TdhVirtualAnchorQueryReqBO request){
        return tdhVirtualAnchorApi.getVirtualAnchorList(request);
    }

    @PostMapping("/anchor/update")
    public Rsp updateVirtualAnchor(@RequestBody TdhVirtualAnchorQueryReqBO request){
        return tdhVirtualAnchorApi.updateEmotion(request);
    }

    /**
     * 数字人面板-我的草稿
     */
    @PostMapping("/draft/list")
    public RspList getCreationRecordList(@RequestBody TdhCreationRecordReqBO request){
        return tdhCreationRecordApi.queryList(request);
    }

    /**
     * 数字人面板-草稿列表（运营平台）
     * @param request
     * @return
     */
    @RequiresRole(value = {UserRoleConstants.sysAdmin,
            UserRoleConstants.tenantAdmin}, logical = Logical.OR)
    @PostMapping("/draft/admin_list")
    public RspList getCreationRecordAdminList(@RequestBody TdhCreationRecordAdminReqBO request){
        return tdhCreationRecordApi.queryAdminList(request);
    }

    /**
     * 根据id查询草稿
     * @param request
     * @return
     */
    @PostMapping("/draft/query")
    public Rsp queryCreation(@RequestBody TdhCreationRecordReqBO request){
        return tdhCreationRecordApi.queryRecord(request);
    }

    /**
     * 数字人面板-更新草稿
     */
    @PostMapping("/draft/save")
    public Rsp updateDraft(@RequestBody TdhCreationRecordReqBO request){
        return tdhCreationRecordApi.save(request);
    }

    /**
     * 数字人面板-我的作品
     */
    @PostMapping("/opus/list")
    public RspList getCreationTaskList(@RequestBody TdhCreationTaskReqBo request){
        return tdhCreationTaskApi.getCreationList(request);
    }

    @PostMapping("/opus/info")
    public Rsp<TdhCreationTaskRspBo> getCreationInfo(@RequestBody TdhCreationTaskReqBo request){
        return tdhCreationTaskApi.getCreationInfo(request);
    }

    /**
     * 数字人面板-更新作品
     */
    @PostMapping("/opus/update")
    public Rsp updateCreationTaskList(@RequestBody TdhCreationTaskReqBo request){
        return tdhCreationTaskApi.update(request);
    }

    /**
     * 数字人面板-删除作品
     */
    @PostMapping("/opus/delete")
    public Rsp deleteTask(@RequestBody TdhCreationTaskReqBo request){
        return tdhCreationTaskApi.delete(request);
    }

    @PostMapping("/audio/save")
    public Rsp saveAudio(@RequestBody TdhAudioBO request){
        return tdhAudioApi.save(request);
    }

    @PostMapping("/audio/query")
    public RspList getAudioList(@RequestBody TdhAudioBO request){
        return tdhAudioApi.query(request);
    }


    @PostMapping("/audio/delete")
    public Rsp deleteAudio(@RequestBody TdhAudioBO request){
        return tdhAudioApi.delete(request);
    }
}
