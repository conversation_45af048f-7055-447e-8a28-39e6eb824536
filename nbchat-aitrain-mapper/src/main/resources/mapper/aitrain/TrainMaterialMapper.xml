<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tydic.nbchat.aitrain.mapper.TrainMaterialMapper">
    <resultMap id="BaseResultMap" type="com.tydic.nbchat.aitrain.mapper.po.TrainMaterial">
        <!--@mbg.generated-->
        <!--@Table train_material-->
        <id column="material_id" jdbcType="VARCHAR" property="materialId"/>
        <result column="tenant_code" jdbcType="VARCHAR" property="tenantCode"/>
        <result column="user_id" jdbcType="VARCHAR" property="userId"/>
        <result column="title" jdbcType="VARCHAR" property="title"/>
        <result column="url" jdbcType="VARCHAR" property="url"/>
        <result column="cover_url" jdbcType="VARCHAR" property="coverUrl"/>
        <result column="type" jdbcType="CHAR" property="type"/>
        <result column="description" jdbcType="LONGVARCHAR" property="description"/>
        <result column="content" jdbcType="LONGVARCHAR" property="content"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="video_duration" jdbcType="INTEGER" property="videoDuration"/>
        <result column="complete_type" jdbcType="CHAR" property="completeType"/>
        <result column="complete_value" jdbcType="INTEGER" property="completeValue"/>
        <result column="order_index" jdbcType="INTEGER" property="orderIndex"/>
        <result column="source" jdbcType="CHAR" property="source"/>
        <result column="category" jdbcType="VARCHAR" property="category"/>
        <result column="label" jdbcType="VARCHAR" property="label"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="create_by" jdbcType="VARCHAR" property="createBy"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="update_by" jdbcType="VARCHAR" property="updateBy"/>
        <result column="view_state" jdbcType="CHAR" property="viewState"/>
        <result column="download_state" jdbcType="CHAR" property="downloadState"/>
        <result column="is_valid" jdbcType="CHAR" property="isValid"/>
        <result column="busi_type" jdbcType="VARCHAR" property="busiType"/>
        <result column="busi_id" jdbcType="VARCHAR" property="busiId"/>
        <result column="file_name" jdbcType="VARCHAR" property="fileName"/>
        <result column="file_size" jdbcType="VARCHAR" property="fileSize"/>

        <result column="courseId" property="courseId"/>
        <result column="courseName" property="courseName"/>
        <result column="courseStartTime" property="courseStartTime"/>
        <result column="studyCount" property="studyCount"/>
        <result column="studyFinishCount" property="studyFinishCount"/>
        <result column="studyingCount" property="studyingCount"/>
        <result column="categoryName" property="categoryName"/>
        <result column="permission" property="permission"/>
    </resultMap>

    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        material_id, tenant_code, user_id, title, url, cover_url, `type`, description, content, remark,
        video_duration, complete_type, complete_value, order_index, `source`, category, `label`,
        create_time, create_by, update_time, update_by, view_state, download_state,
        is_valid,busi_type,busi_id,file_name,file_size
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
        <!--@mbg.generated-->
        select tm.*,
        stc.cate_name as categoryName
        from train_material tm
        left join
        sys_tree_category stc
        on tm.category = stc.cate_id
        where tm.material_id = #{materialId,jdbcType=VARCHAR}
    </select>
    <insert id="insert" parameterType="com.tydic.nbchat.aitrain.mapper.po.TrainMaterial">
        <!--@mbg.generated-->
        insert into train_material (material_id, tenant_code, user_id,
        title, url, cover_url, `type`, description,
        content, remark, video_duration,
        complete_type, complete_value, order_index,
        `source`, category, `label`,
        create_time, create_by, update_time,
        update_by, view_state, download_state,
        is_valid,busi_type,busi_id,file_name,file_size)
        values (#{materialId,jdbcType=VARCHAR}, #{tenantCode,jdbcType=VARCHAR}, #{userId,jdbcType=VARCHAR},
        #{title,jdbcType=VARCHAR}, #{url,jdbcType=VARCHAR}, #{coverUrl,jdbcType=VARCHAR}, #{type,jdbcType=CHAR},
        #{description,jdbcType=LONGVARCHAR},
        #{content,jdbcType=LONGVARCHAR}, #{remark,jdbcType=VARCHAR}, #{videoDuration,jdbcType=INTEGER},
        #{completeType,jdbcType=CHAR}, #{completeValue,jdbcType=INTEGER}, #{orderIndex,jdbcType=INTEGER},
        #{source,jdbcType=CHAR}, #{category,jdbcType=VARCHAR}, #{label,jdbcType=VARCHAR},
        #{createTime,jdbcType=TIMESTAMP}, #{createBy,jdbcType=VARCHAR}, #{updateTime,jdbcType=TIMESTAMP},
        #{updateBy,jdbcType=VARCHAR}, #{viewState,jdbcType=CHAR}, #{downloadState,jdbcType=CHAR},
        #{isValid,jdbcType=CHAR},#{busiType,jdbcType=VARCHAR},#{busiId,jdbcType=VARCHAR},#{fileName,jdbcType=VARCHAR},#{fileSize,jdbcType=VARCHAR})
    </insert>
    <insert id="insertSelective" parameterType="com.tydic.nbchat.aitrain.mapper.po.TrainMaterial">
        <!--@mbg.generated-->
        insert into train_material
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="materialId != null">
                material_id,
            </if>
            <if test="tenantCode != null">
                tenant_code,
            </if>
            <if test="userId != null">
                user_id,
            </if>
            <if test="title != null">
                title,
            </if>
            <if test="url != null">
                url,
            </if>
            <if test="coverUrl != null">
                cover_url,
            </if>
            <if test="type != null">
                `type`,
            </if>
            <if test="description != null">
                description,
            </if>
            <if test="content != null">
                content,
            </if>
            <if test="remark != null">
                remark,
            </if>
            <if test="videoDuration != null">
                video_duration,
            </if>
            <if test="completeType != null">
                complete_type,
            </if>
            <if test="completeValue != null">
                complete_value,
            </if>
            <if test="orderIndex != null">
                order_index,
            </if>
            <if test="source != null">
                `source`,
            </if>
            <if test="category != null">
                category,
            </if>
            <if test="label != null">
                `label`,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="createBy != null">
                create_by,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="updateBy != null">
                update_by,
            </if>
            <if test="viewState != null">
                view_state,
            </if>
            <if test="downloadState != null">
                download_state,
            </if>
            <if test="isValid != null">
                is_valid,
            </if>
            <if test="busiType != null">
                busi_type,
            </if>
            <if test="busiId != null">
                busi_id,
            </if>
            <if test="fileName != null">
                file_name,
            </if>
            <if test="fileSize != null">
                file_size,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="materialId != null">
                #{materialId,jdbcType=VARCHAR},
            </if>
            <if test="tenantCode != null">
                #{tenantCode,jdbcType=VARCHAR},
            </if>
            <if test="userId != null">
                #{userId,jdbcType=VARCHAR},
            </if>
            <if test="title != null">
                #{title,jdbcType=VARCHAR},
            </if>
            <if test="url != null">
                #{url,jdbcType=VARCHAR},
            </if>
            <if test="coverUrl != null">
                #{coverUrl,jdbcType=VARCHAR},
            </if>
            <if test="type != null">
                #{type,jdbcType=CHAR},
            </if>
            <if test="description != null">
                #{description,jdbcType=LONGVARCHAR},
            </if>
            <if test="content != null">
                #{content,jdbcType=LONGVARCHAR},
            </if>
            <if test="remark != null">
                #{remark,jdbcType=VARCHAR},
            </if>
            <if test="videoDuration != null">
                #{videoDuration,jdbcType=INTEGER},
            </if>
            <if test="completeType != null">
                #{completeType,jdbcType=CHAR},
            </if>
            <if test="completeValue != null">
                #{completeValue,jdbcType=INTEGER},
            </if>
            <if test="orderIndex != null">
                #{orderIndex,jdbcType=INTEGER},
            </if>
            <if test="source != null">
                #{source,jdbcType=CHAR},
            </if>
            <if test="category != null">
                #{category,jdbcType=VARCHAR},
            </if>
            <if test="label != null">
                #{label,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createBy != null">
                #{createBy,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateBy != null">
                #{updateBy,jdbcType=VARCHAR},
            </if>
            <if test="viewState != null">
                #{viewState,jdbcType=CHAR},
            </if>
            <if test="downloadState != null">
                #{downloadState,jdbcType=CHAR},
            </if>
            <if test="isValid != null">
                #{isValid,jdbcType=CHAR},
            </if>
            <if test="busiType != null">
                #{busiType,jdbcType=VARCHAR},
            </if>
            <if test="busiId != null">
                #{busiId,jdbcType=VARCHAR},
            </if>
            <if test="fileName != null">
                #{fileName,jdbcType=VARCHAR},
            </if>
            <if test="fileSize != null">
                #{fileSize,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.tydic.nbchat.aitrain.mapper.po.TrainMaterial">
        <!--@mbg.generated-->
        update train_material
        <set>
            <if test="tenantCode != null">
                tenant_code = #{tenantCode,jdbcType=VARCHAR},
            </if>
            <if test="userId != null">
                user_id = #{userId,jdbcType=VARCHAR},
            </if>
            <if test="title != null">
                title = #{title,jdbcType=VARCHAR},
            </if>
            <if test="url != null">
                url = #{url,jdbcType=VARCHAR},
            </if>
            <if test="coverUrl != null">
                cover_url = #{coverUrl,jdbcType=VARCHAR},
            </if>
            <if test="type != null">
                `type` = #{type,jdbcType=CHAR},
            </if>
            <if test="description != null">
                description = #{description,jdbcType=LONGVARCHAR},
            </if>
            <if test="content != null">
                content = #{content,jdbcType=LONGVARCHAR},
            </if>
            <if test="remark != null">
                remark = #{remark,jdbcType=VARCHAR},
            </if>
            <if test="videoDuration != null">
                video_duration = #{videoDuration,jdbcType=INTEGER},
            </if>
            <if test="completeType != null">
                complete_type = #{completeType,jdbcType=CHAR},
            </if>
            <if test="completeValue != null">
                complete_value = #{completeValue,jdbcType=INTEGER},
            </if>
            <if test="orderIndex != null">
                order_index = #{orderIndex,jdbcType=INTEGER},
            </if>
            <if test="source != null">
                `source` = #{source,jdbcType=CHAR},
            </if>
            <if test="category != null">
                category = #{category,jdbcType=VARCHAR},
            </if>
            <if test="label != null">
                `label` = #{label,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createBy != null">
                create_by = #{createBy,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateBy != null">
                update_by = #{updateBy,jdbcType=VARCHAR},
            </if>
            <if test="viewState != null">
                view_state = #{viewState,jdbcType=CHAR},
            </if>
            <if test="downloadState != null">
                download_state = #{downloadState,jdbcType=CHAR},
            </if>
            <if test="isValid != null">
                is_valid = #{isValid,jdbcType=CHAR},
            </if>
            <if test="busiType != null">
                busi_type = #{busiType,jdbcType=VARCHAR},
            </if>
            <if test="busiId != null">
                busi_id = #{busiId,jdbcType=VARCHAR},
            </if>
            <if test="fileName != null">
                file_name = #{fileName,jdbcType=VARCHAR},
            </if>
            <if test="fileSize != null">
                file_size = #{fileSize,jdbcType=VARCHAR},
            </if>
        </set>
        where material_id = #{materialId,jdbcType=VARCHAR}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.tydic.nbchat.aitrain.mapper.po.TrainMaterial">
        <!--@mbg.generated-->
        update train_material
        set tenant_code = #{tenantCode,jdbcType=VARCHAR},
        user_id = #{userId,jdbcType=VARCHAR},
        title = #{title,jdbcType=VARCHAR},
        url = #{url,jdbcType=VARCHAR},
        cover_url = #{coverUrl,jdbcType=VARCHAR},
        `type` = #{type,jdbcType=CHAR},
        description = #{description,jdbcType=LONGVARCHAR},
        content = #{content,jdbcType=LONGVARCHAR},
        remark = #{remark,jdbcType=VARCHAR},
        video_duration = #{videoDuration,jdbcType=INTEGER},
        complete_type = #{completeType,jdbcType=CHAR},
        complete_value = #{completeValue,jdbcType=INTEGER},
        order_index = #{orderIndex,jdbcType=INTEGER},
        `source` = #{source,jdbcType=CHAR},
        category = #{category,jdbcType=VARCHAR},
        `label` = #{label,jdbcType=VARCHAR},
        create_time = #{createTime,jdbcType=TIMESTAMP},
        create_by = #{createBy,jdbcType=VARCHAR},
        update_time = #{updateTime,jdbcType=TIMESTAMP},
        update_by = #{updateBy,jdbcType=VARCHAR},
        view_state = #{viewState,jdbcType=CHAR},
        download_state = #{downloadState,jdbcType=CHAR},
        is_valid = #{isValid,jdbcType=CHAR},
        busi_type = #{busiType,jdbcType=VARCHAR},
        busi_id = #{busiId,jdbcType=VARCHAR},
        file_name = #{fileName,jdbcType=VARCHAR},
        file_size = #{fileSize,jdbcType=VARCHAR}
        where material_id = #{materialId,jdbcType=VARCHAR}
    </update>
    <select id="selectByAll" resultMap="BaseResultMap">
        <!--@mbg.generated-->
        select
        tm.*,
        stc.cate_name as categoryName
        <if test="permissionFlag == '1'.toString()">
            ,IFNULL(max(dp.permission),'0') as permission
        </if>
        from train_material tm
        left join
        sys_tree_category stc
        on tm.category = stc.cate_id
        <if test="permissionFlag == '1'.toString()">
            LEFT JOIN
            sys_data_permission dp
            ON
            tm.material_id = dp.busi_id
            AND dp.busi_type = 'train_material'
            AND tm.tenant_code = #{tenantCode}
            AND(
            dp.user_id=#{userId}
            <if test="deptId != null and deptId != ''">
                or dp.dept_id=#{deptId}
            </if>
            <if test="roleIds != null and roleIds.size() > 0">
                or dp.role_id in(
                <foreach collection="roleIds" item="item" separator=",">
                    #{item}
                </foreach>
                )
            </if>
            <if test="postIds != null and postIds.size() > 0">
                or dp.post_id in (
                <foreach collection="postIds" item="item" separator=",">
                    #{item}
                </foreach>
                )
            </if>
            )
        </if>
        <where>
            <if test="permissionFlag == '1'.toString()">
                (
                tm.view_state = '1'
                <if test="userId != null and userId != ''">
                    or tm.user_id=#{userId}
                    or dp.user_id=#{userId}
                </if>
                <if test="deptId != null and deptId != ''">
                    or dp.dept_id=#{deptId}
                </if>
                <if test="roleIds != null and roleIds.size() > 0">
                    or dp.role_id in(
                    <foreach collection="roleIds" item="item" separator=",">
                        #{item}
                    </foreach>
                    )
                </if>
                <if test="postIds != null and postIds.size() > 0">
                    or dp.post_id in (
                    <foreach collection="postIds" item="item" separator=",">
                        #{item}
                    </foreach>
                    )
                </if>
                )
            </if>
            and tm.is_valid='1'
            <if test="materialId != null and materialId != ''">
                and tm.material_id=#{materialId,jdbcType=VARCHAR}
            </if>
            <if test="tenantCode != null and tenantCode != ''">
                and tm.tenant_code=#{tenantCode,jdbcType=VARCHAR}
            </if>
            <if test="isMyCreate == '1'.toString() and userId != null and userId != ''">
                and tm.user_id=#{userId,jdbcType=VARCHAR}
            </if>
            <if test="title != null and title != ''">
                and tm.title like concat('%',#{title,jdbcType=VARCHAR},'%')
            </if>
            <if test="url != null and url != ''">
                and tm.url=#{url,jdbcType=VARCHAR}
            </if>
            <if test="coverUrl != null and coverUrl != ''">
                and tm.cover_url = #{coverUrl,jdbcType=VARCHAR}
            </if>
            <if test="type != null and type != ''">
                <if test='type eq "1"'>
                    and tm.`type` in ('mp4', 'flv', 'mov')
                </if>
                <if test='type eq "2"'>
                    and tm.`type` in ('mp3', 'wav')
                </if>
                <if test='type eq "3"'>
                    and tm.`type` in ('doc', 'docx', 'ppt', 'pptx', 'xls', 'xlsx', 'pdf', 'txt')
                </if>
            </if>
            <if test="description != null and description != ''">
                and tm.description=#{description,jdbcType=LONGVARCHAR}
            </if>
            <if test="content != null and content != ''">
                and tm.content=#{content,jdbcType=LONGVARCHAR}
            </if>
            <if test="remark != null and remark != ''">
                and tm.remark=#{remark,jdbcType=VARCHAR}
            </if>
            <if test="videoDuration != null">
                and tm.video_duration=#{videoDuration,jdbcType=INTEGER}
            </if>
            <if test="completeType != null and completeType != ''">
                and tm.complete_type=#{completeType,jdbcType=CHAR}
            </if>
            <if test="completeValue != null">
                and tm.complete_value=#{completeValue,jdbcType=INTEGER}
            </if>
            <if test="orderIndex != null">
                and tm.order_index=#{orderIndex,jdbcType=INTEGER}
            </if>
            <if test="source != null and source != ''">
                and tm.`source`=#{source,jdbcType=CHAR}
            </if>
            <if test="category != null and category != ''">
                and tm.category=#{category,jdbcType=VARCHAR}
            </if>
            <if test="label != null and label != ''">
                and tm.`label`=#{label,jdbcType=VARCHAR}
            </if>
            <if test="createTime != null">
                and tm.create_time=#{createTime,jdbcType=TIMESTAMP}
            </if>
            <if test="createStartTime">
                and tm.create_time >= #{createStartTime,jdbcType=TIMESTAMP}
            </if>
            <if test="createEndTime">
                and tm.create_time &lt; #{createEndTime,jdbcType=TIMESTAMP}
            </if>
            <if test="createBy != null and createBy != ''">
                and tm.create_by like concat('%',#{createBy,jdbcType=VARCHAR},'%')
            </if>
            <if test="updateTime != null">
                and tm.update_time=#{updateTime,jdbcType=TIMESTAMP}
            </if>
            <if test="updateBy != null and updateBy != ''">
                and tm.update_by=#{updateBy,jdbcType=VARCHAR}
            </if>
            <if test="viewState != null and viewState != ''">
                and tm.view_state=#{viewState,jdbcType=CHAR}
            </if>
            <if test="downloadState != null and downloadState != ''">
                and tm.download_state=#{downloadState,jdbcType=CHAR}
            </if>
            <if test="busiType != null and busiType != ''">
                and tm.busi_type=#{busiType,jdbcType=VARCHAR}
            </if>
            <if test="busiId != null and busiId != ''">
                and tm.busi_id=#{busiId,jdbcType=VARCHAR}
            </if>
            <if test="keyWord != null and keyWord != ''">
                and (
                tm.title like concat('%',#{keyWord,jdbcType=VARCHAR},'%')
                or tm.description like concat('%',#{keyWord,jdbcType=VARCHAR},'%')
                or tm.content like concat('%',#{keyWord,jdbcType=VARCHAR},'%')
                or tm.remark like concat('%',#{keyWord,jdbcType=VARCHAR},'%')
                )
            </if>
        </where>
        <if test="permissionFlag == '1'.toString()">
            group by tm.material_id
        </if>
        order by tm.create_time desc
    </select>
    <update id="updateBatchSelective" parameterType="java.util.List">
        <!--@mbg.generated-->
        update train_material
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="tenant_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.tenantCode != null">
                        when material_id = #{item.materialId,jdbcType=VARCHAR} then #{item.tenantCode,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="user_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.userId != null">
                        when material_id = #{item.materialId,jdbcType=VARCHAR} then #{item.userId,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="title = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.title != null">
                        when material_id = #{item.materialId,jdbcType=VARCHAR} then #{item.title,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="url = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.url != null">
                        when material_id = #{item.materialId,jdbcType=VARCHAR} then #{item.url,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="cover_url = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.coverUrl != null">
                        when material_id = #{item.materialId,jdbcType=VARCHAR} then #{item.coverUrl,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="`type` = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.type != null">
                        when material_id = #{item.materialId,jdbcType=VARCHAR} then #{item.type,jdbcType=CHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="description = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.description != null">
                        when material_id = #{item.materialId,jdbcType=VARCHAR} then
                        #{item.description,jdbcType=LONGVARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="content = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.content != null">
                        when material_id = #{item.materialId,jdbcType=VARCHAR} then #{item.content,jdbcType=LONGVARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="remark = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.remark != null">
                        when material_id = #{item.materialId,jdbcType=VARCHAR} then #{item.remark,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="video_duration = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.videoDuration != null">
                        when material_id = #{item.materialId,jdbcType=VARCHAR} then
                        #{item.videoDuration,jdbcType=INTEGER}
                    </if>
                </foreach>
            </trim>
            <trim prefix="complete_type = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.completeType != null">
                        when material_id = #{item.materialId,jdbcType=VARCHAR} then #{item.completeType,jdbcType=CHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="complete_value = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.completeValue != null">
                        when material_id = #{item.materialId,jdbcType=VARCHAR} then
                        #{item.completeValue,jdbcType=INTEGER}
                    </if>
                </foreach>
            </trim>
            <trim prefix="order_index = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.orderIndex != null">
                        when material_id = #{item.materialId,jdbcType=VARCHAR} then #{item.orderIndex,jdbcType=INTEGER}
                    </if>
                </foreach>
            </trim>
            <trim prefix="`source` = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.source != null">
                        when material_id = #{item.materialId,jdbcType=VARCHAR} then #{item.source,jdbcType=CHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="category = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.category != null">
                        when material_id = #{item.materialId,jdbcType=VARCHAR} then #{item.category,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="`label` = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.label != null">
                        when material_id = #{item.materialId,jdbcType=VARCHAR} then #{item.label,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.createTime != null">
                        when material_id = #{item.materialId,jdbcType=VARCHAR} then
                        #{item.createTime,jdbcType=TIMESTAMP}
                    </if>
                </foreach>
            </trim>
            <trim prefix="create_by = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.createBy != null">
                        when material_id = #{item.materialId,jdbcType=VARCHAR} then #{item.createBy,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="update_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.updateTime != null">
                        when material_id = #{item.materialId,jdbcType=VARCHAR} then
                        #{item.updateTime,jdbcType=TIMESTAMP}
                    </if>
                </foreach>
            </trim>
            <trim prefix="update_by = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.updateBy != null">
                        when material_id = #{item.materialId,jdbcType=VARCHAR} then #{item.updateBy,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="view_state = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.viewState != null">
                        when material_id = #{item.materialId,jdbcType=VARCHAR} then #{item.viewState,jdbcType=CHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="download_state = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.downloadState != null">
                        when material_id = #{item.materialId,jdbcType=VARCHAR} then #{item.downloadState,jdbcType=CHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="is_valid = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.isValid != null">
                        when material_id = #{item.materialId,jdbcType=VARCHAR} then #{item.isValid,jdbcType=CHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="busi_type = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.busiType != null">
                        when material_id = #{item.materialId,jdbcType=VARCHAR} then #{item.busiType,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="busi_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.busiId != null">
                        when material_id = #{item.materialId,jdbcType=VARCHAR} then #{item.busiId,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="file_name = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.fileName != null">
                        when material_id = #{item.materialId,jdbcType=VARCHAR} then #{item.fileName,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="file_size = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.fileSize != null">
                        when material_id = #{item.materialId,jdbcType=VARCHAR} then #{item.fileSize,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
        </trim>
        where material_id in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.materialId,jdbcType=VARCHAR}
        </foreach>
    </update>
    <insert id="batchInsert" parameterType="map">
        <!--@mbg.generated-->
        insert into train_material
        (material_id, tenant_code, user_id, title, url, cover_url, `type`, description, content, remark,
        video_duration, complete_type, complete_value, order_index, `source`, category,
        `label`, create_time, create_by, update_time, update_by, view_state, download_state,
        is_valid, busi_type, busi_id, file_name, file_size)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.materialId,jdbcType=VARCHAR}, #{item.tenantCode,jdbcType=VARCHAR}, #{item.userId,jdbcType=VARCHAR},
            #{item.title,jdbcType=VARCHAR}, #{item.url,jdbcType=VARCHAR}, #{item.coverUrl,jdbcType=VARCHAR},
            #{item.type,jdbcType=CHAR},
            #{item.description,jdbcType=LONGVARCHAR}, #{item.content,jdbcType=LONGVARCHAR},
            #{item.remark,jdbcType=VARCHAR}, #{item.videoDuration,jdbcType=INTEGER}, #{item.completeType,jdbcType=CHAR},
            #{item.completeValue,jdbcType=INTEGER}, #{item.orderIndex,jdbcType=INTEGER}, #{item.source,jdbcType=CHAR},
            #{item.category,jdbcType=VARCHAR}, #{item.label,jdbcType=VARCHAR}, #{item.createTime,jdbcType=TIMESTAMP},
            #{item.createBy,jdbcType=VARCHAR}, #{item.updateTime,jdbcType=TIMESTAMP}, #{item.updateBy,jdbcType=VARCHAR},
            #{item.viewState,jdbcType=CHAR}, #{item.downloadState,jdbcType=CHAR}, #{item.isValid,jdbcType=CHAR},
            #{item.busiType,jdbcType=VARCHAR}, #{item.busiId,jdbcType=VARCHAR}, #{item.fileName,jdbcType=VARCHAR},
            #{item.fileSize,jdbcType=VARCHAR}
            )
        </foreach>
    </insert>

    <delete id="deleteAllByMaterialIds">
        update train_material
        set is_valid = '0'
        where material_id in (
        <foreach collection="materialIds" item="item" separator=",">
            #{item,jdbcType=VARCHAR}
        </foreach>
        )
    </delete>

    <select id="selectByStat" resultMap="BaseResultMap">
        select tc.course_id as courseId,
        tc.course_name as courseName,
        tc.create_time as courseStartTime,
        (select count(0)
        from train_course_record
        where course_state = '2'
        and tenant_code = tc.tenant_code
        and course_id = tc.course_id) as studyFinishCount,
        (select count(0)
        from train_course_record
        where course_state = '1'
        and tenant_code = tc.tenant_code
        and course_id = tc.course_id) as studyingCount,
        (select count(0)
        from train_course_record
        where tenant_code = tc.tenant_code
        and course_id = tc.course_id) as studyCount
        from train_course tc
        where tc.tenant_code = #{tenantCode,jdbcType=VARCHAR}
        and tc.is_valid = '1'
        and tc.course_id in (select course_id
        from train_course_part
        where material_id = #{materialId,jdbcType=VARCHAR}
        and is_valid = '1')
        order by tc.create_time desc
    </select>
</mapper>
