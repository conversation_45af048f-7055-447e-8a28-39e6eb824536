package com.tydic.nbchat.admin.core.controller;


import com.tydic.nbchat.admin.api.SysTenantManagementApi;
import com.tydic.nbchat.admin.api.bo.SysTenantManagementReqBO;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.base.bo.RspList;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/admin/tenant/management")
public class SysTenantManagementController {

    private final SysTenantManagementApi sysTenantManagementApi;

    /**
     * 创建租户
     * @param @param reqBO
     * @return @return {@link Rsp }
     */
    @PostMapping("/createTenant")
    public Rsp createTenant(@RequestBody SysTenantManagementReqBO reqBO){
        return sysTenantManagementApi.createTenant(reqBO);
    }

    /**
     * 查询租户信息
     * @param @param reqBO
     * @return @return {@link Rsp }
     */
    @PostMapping("/list")
    public RspList getTenantInformation(@RequestBody SysTenantManagementReqBO reqBO){
        return sysTenantManagementApi.getTenantList(reqBO);
    }

    /**
     * 更新租户信息
     * @param @param reqBO
     * @return @return {@link Rsp }
     */
    @PostMapping("/updateTenantInformation")
    public Rsp updateTenant(@RequestBody SysTenantManagementReqBO reqBO){
        return sysTenantManagementApi.updateTenant(reqBO);
    }

    /**
     * 查询租户定制配置
     * @param @param reqBO
     * @return @return {@link Rsp }
     */
    @PostMapping("/custom/config")
    public Rsp getCustomConfig(@RequestBody SysTenantManagementReqBO reqBO){
        return sysTenantManagementApi.queryCustomConfig(reqBO);
    }

    @PostMapping("/invalid")
    public Rsp invalid(@RequestBody SysTenantManagementReqBO reqBO){
        return sysTenantManagementApi.invalid(reqBO);
    }

}
