package com.tydic.nbchat.admin.core.controller;

import com.tydic.nbchat.admin.api.qihu.QihuApiCoordinationService;
import com.tydic.nbchat.admin.api.bo.qihu.QihuApiCoordinationReqBO;
import com.tydic.nicc.dc.base.bo.Rsp;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

/**
 * 360广告主回传数据接口控制器
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/admin/qihu")
public class QihuApiCoordinationController {

    private final QihuApiCoordinationService qihuApiCoordinationService;

    /**
     * 360广告主回传转化数据接口
     * 
     * @param reqBo 请求参数
     * @return 响应结果
     */
    @PostMapping("/dataConversionCallback")
    public Rsp dataConversionCallback(@RequestBody QihuApiCoordinationReqBO reqBo) {
        log.info("360转化数据回传请求: {}", reqBo);
        return qihuApiCoordinationService.dataConversionCallback(reqBo);
    }
}
