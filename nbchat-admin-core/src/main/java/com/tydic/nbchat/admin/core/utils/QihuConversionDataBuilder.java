package com.tydic.nbchat.admin.core.utils;

import com.tydic.nbchat.admin.api.bo.qihu.QihuConversionDataBO;
import com.tydic.nbchat.admin.api.bo.qihu.QihuDataDetailBO;

/**
 * 360转化数据构建工具类
 */
public class QihuConversionDataBuilder {

    /**
     * 构建PC搜索推广转化数据
     * 
     * @param qhclickid 360点击ID
     * @param transId 事务ID
     * @param event 事件类型 (SUBMIT/ORDER)
     * @return 转化数据
     */
    public static QihuConversionDataBO buildPcSearchConvert(String qhclickid, String transId, String event) {
        QihuConversionDataBO conversionData = new QihuConversionDataBO();
        conversionData.setRequestTime(System.currentTimeMillis() / 1000);
        conversionData.setDataIndustry("ocpc_ps_convert");
        
        QihuDataDetailBO dataDetail = new QihuDataDetailBO();
        dataDetail.setQhclickid(qhclickid);
        dataDetail.setTransId(transId);
        dataDetail.setEvent(event);
        dataDetail.setEventTime(System.currentTimeMillis() / 1000);
        
        conversionData.setDataDetail(dataDetail);
        return conversionData;
    }

    /**
     * 构建移动搜索转化数据
     * 
     * @param qhclickid 360点击ID
     * @param transId 事务ID
     * @param event 事件类型 (SUBMIT/ORDER)
     * @return 转化数据
     */
    public static QihuConversionDataBO buildMobileSearchConvert(String qhclickid, String transId, String event) {
        QihuConversionDataBO conversionData = new QihuConversionDataBO();
        conversionData.setRequestTime(System.currentTimeMillis() / 1000);
        conversionData.setDataIndustry("ocpc_ms_convert");
        
        QihuDataDetailBO dataDetail = new QihuDataDetailBO();
        dataDetail.setQhclickid(qhclickid);
        dataDetail.setTransId(transId);
        dataDetail.setEvent(event);
        dataDetail.setEventTime(System.currentTimeMillis() / 1000);
        
        conversionData.setDataDetail(dataDetail);
        return conversionData;
    }

    /**
     * 构建展示广告转化数据
     * 
     * @param qhclickid 360点击ID
     * @param jzqs 精准匹配参数
     * @param transId 事务ID
     * @param event 事件类型 (SUBMIT/ORDER)
     * @return 转化数据
     */
    public static QihuConversionDataBO buildDisplayAdConvert(String qhclickid, String jzqs, String transId, String event) {
        QihuConversionDataBO conversionData = new QihuConversionDataBO();
        conversionData.setRequestTime(System.currentTimeMillis() / 1000);
        conversionData.setDataIndustry("ocpc_zs_convert");
        
        QihuDataDetailBO dataDetail = new QihuDataDetailBO();
        dataDetail.setQhclickid(qhclickid);
        dataDetail.setJzqs(jzqs);
        dataDetail.setTransId(transId);
        dataDetail.setEvent(event);
        dataDetail.setEventTime(System.currentTimeMillis() / 1000);
        
        conversionData.setDataDetail(dataDetail);
        return conversionData;
    }

    /**
     * 构建移动推广转化数据
     * 
     * @param impressionId 展示ID
     * @param transId 事务ID
     * @param event 事件类型 (SUBMIT/ORDER)
     * @return 转化数据
     */
    public static QihuConversionDataBO buildMobilePromotionConvert(String impressionId, String transId, String event) {
        QihuConversionDataBO conversionData = new QihuConversionDataBO();
        conversionData.setRequestTime(System.currentTimeMillis() / 1000);
        conversionData.setDataIndustry("ocpc_web_convert");
        
        QihuDataDetailBO dataDetail = new QihuDataDetailBO();
        dataDetail.setImpressionId(impressionId);
        dataDetail.setTransId(transId);
        dataDetail.setEvent(event);
        dataDetail.setEventTime(System.currentTimeMillis() / 1000);
        
        conversionData.setDataDetail(dataDetail);
        return conversionData;
    }

    /**
     * 构建自定义转化数据
     * 
     * @param dataIndustry 数据行业类型
     * @param dataDetail 数据详情
     * @return 转化数据
     */
    public static QihuConversionDataBO buildCustomConvert(String dataIndustry, QihuDataDetailBO dataDetail) {
        QihuConversionDataBO conversionData = new QihuConversionDataBO();
        conversionData.setRequestTime(System.currentTimeMillis() / 1000);
        conversionData.setDataIndustry(dataIndustry);
        conversionData.setDataDetail(dataDetail);
        return conversionData;
    }
}
