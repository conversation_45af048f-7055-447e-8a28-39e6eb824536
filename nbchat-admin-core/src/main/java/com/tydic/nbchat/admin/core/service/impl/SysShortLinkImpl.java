package com.tydic.nbchat.admin.core.service.impl;

import com.tydic.nbchat.admin.api.SysShortLinkApi;
import com.tydic.nbchat.admin.api.bo.sentence.ShortLinkReqBO;
import com.tydic.nbchat.admin.core.config.properties.ShortLinkConfigProperties;
import com.tydic.nbchat.admin.core.config.utils.RandomStringGenerator;
import com.tydic.nbchat.admin.mapper.SysShortLinkMapper;
import com.tydic.nbchat.admin.mapper.SysShortLinkVisitMapper;
import com.tydic.nbchat.admin.mapper.po.SysShortLink;
import com.tydic.nbchat.admin.mapper.po.SysShortLinkVisit;
import com.tydic.nbchat.user.api.UserInviteApi;
import com.tydic.nbchat.user.api.bo.UserInfoReqBO;
import com.tydic.nbchat.user.api.bo.invite.InviteCodeResBO;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.boot.starter.util.BaseRspUtils;
import com.tydic.nicc.dc.boot.starter.util.DateTimeUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @create 2025/6/9
 * @since 1.0.0
 **/
@Slf4j
@Service
public class SysShortLinkImpl implements SysShortLinkApi {

    @Resource
    private ShortLinkConfigProperties shortLinkConfigProperties;

    @Resource
    private SysShortLinkMapper sysShortLinkMapper;

    @Resource
    private SysShortLinkVisitMapper sysShortLinkVisitMapper;

    @DubboReference(version = "${nicc-dc-config.dubbo-provider.version}",
            group = "${nicc-dc-config.dubbo-provider.group}", timeout = 2000)
    private UserInviteApi userInviteApi;
    //短链接长度
    private static final Integer SHORT_LINK_LENGTH = 12;

    @Override
    public Rsp getShortLinkUrl(ShortLinkReqBO req) {
        String bizId = req.getBizId();
        String userId = req.get_userId();
        SysShortLink sysShortLink = sysShortLinkMapper.selectByBizTypeAndId(req.getBizType(),bizId);
        String shareCode = "";
        if (sysShortLink == null){
            shareCode = RandomStringGenerator.generateRandomString(SHORT_LINK_LENGTH);
            //分享链接不存在生成分享链接，入库
            sysShortLink = new SysShortLink();
            sysShortLink.setTenantCode(req.getTenantCode());
            sysShortLink.setShortCode(shareCode);
            //原链接
            String originUrl = shortLinkConfigProperties.getVideoSharePrefix() +"taskId="+bizId;
            sysShortLink.setOriginUrl(originUrl);
            sysShortLink.setBizType(req.getBizType());
            sysShortLink.setBizId(bizId);
            sysShortLink.setUserId(userId);
            if(req.getExpiredDay() != -1){
                sysShortLink.setExpireTime(DateTimeUtil.DateAddDayOfYear(req.getExpiredDay()));
            }else {
                sysShortLink.setExpireTime(DateTimeUtil.convertAsDate("9999-01-01 00:00:00"));
            }
            sysShortLink.setCreateTime(new Date());
            sysShortLinkMapper.insert(sysShortLink);
        }else{
            shareCode = sysShortLink.getShortCode();
        }
        Map<String,String> resultMap = new HashMap<>();
        resultMap.put("shortLinkUrl" , shortLinkConfigProperties.getSLinkPrefix()+shareCode);
        return BaseRspUtils.createSuccessRsp(resultMap);
    }

    @Override
    public Rsp queryShareUrlByCode(String shareCode) {
        if(!isValidShareCode(shareCode)){
            log.info("短链接分享码格式错误");
            return BaseRspUtils.createErrorRsp("分享码格式错误");
        }
        //查询未过期的分享码 数据
        SysShortLink sysShortLink = sysShortLinkMapper.selectByShortCode(shareCode);
        if (sysShortLink == null){
            log.info("短链接分享码已过期或不存在shareCode={}",shareCode);
            return BaseRspUtils.createErrorRsp("分享码已过期或不存在");
        }
        String originUrl = sysShortLink.getOriginUrl();
        String userId = sysShortLink.getUserId();
        //根据userId查询邀请码
        UserInfoReqBO userInfoReqBO = new UserInfoReqBO();
        userInfoReqBO.setUserId(userId);
        Rsp<InviteCodeResBO> inviteCode = userInviteApi.getInviteCode(userInfoReqBO);
        log.info("查询邀请码返回shareCode={},inviteCode={}",shareCode,inviteCode);
        if (inviteCode != null && inviteCode.isSuccess()){
            InviteCodeResBO data = inviteCode.getData();
            if(data != null && StringUtils.isNotEmpty(data.getInviteCode())){
                originUrl += "&yqm="+data.getInviteCode();
            }
        }
        return BaseRspUtils.createSuccessRsp(originUrl);
    }

    @Override
    public void saveShortLinkVisitData(String shortCode,String clientIp,String userAgent) {
        //更新访问时间
        sysShortLinkMapper.updateVisitTime(shortCode,new Date());
        SysShortLinkVisit sysShortLinkVisit = new SysShortLinkVisit();
        sysShortLinkVisit.setShortCode(shortCode);
        sysShortLinkVisit.setVisitTime(new Date());
        sysShortLinkVisit.setClientIp(clientIp);
        sysShortLinkVisit.setUserAgent(userAgent);
        sysShortLinkVisitMapper.insert(sysShortLinkVisit);
    }

    /**
     * 校验分享码格式
     * @param shareCode 分享码
     * @return 是否有效
     */
    private boolean isValidShareCode(String shareCode) {
        // 检查是否为null或长度不为12
        if (shareCode == null || shareCode.length() != SHORT_LINK_LENGTH) {
            return false;
        }

        // 检查是否只包含大小写字母和数字
        for (char c : shareCode.toCharArray()) {
            if (!Character.isLetterOrDigit(c)) {
                return false;
            }
        }

        return true;
    }


}
