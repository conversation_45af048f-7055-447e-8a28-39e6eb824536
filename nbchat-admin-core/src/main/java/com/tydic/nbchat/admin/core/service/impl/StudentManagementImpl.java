package com.tydic.nbchat.admin.core.service.impl;

import com.alibaba.excel.EasyExcel;
import com.tydic.nbchat.admin.api.StudentManagementApi;
import com.tydic.nbchat.admin.api.bo.SysTenantOptUserReqBO;
import com.tydic.nbchat.admin.api.bo.SysTenantUserInfoBO;
import com.tydic.nbchat.admin.api.bo.dept.SysDeptUserQueryReqBO;
import com.tydic.nbchat.admin.api.bo.dept.SysDeptUserQueryRspBO;
import com.tydic.nbchat.admin.api.bo.dept.SysDeptUserSaveReqBO;
import com.tydic.nbchat.admin.core.busi.RpUserDetailBusiService;
import com.tydic.nbchat.admin.core.busi.SysTenantUserBuisService;
import com.tydic.nbchat.admin.core.busi.SysUserRoleBusiService;
import com.tydic.nbchat.admin.core.config.properties.NbchatAdminConfigProperties;
import com.tydic.nbchat.admin.mapper.NbchatSysUserTenantMapper;
import com.tydic.nbchat.admin.mapper.SysPostMapper;
import com.tydic.nbchat.admin.mapper.po.ImportUserInfo;
import com.tydic.nbchat.admin.mapper.po.NbchatSysUserTenant;
import com.tydic.nbchat.user.api.NbchatUserApi;
import com.tydic.nbchat.user.api.NbchatUserRegLoginApi;
import com.tydic.nbchat.user.api.UserBaseInfoApi;
import com.tydic.nbchat.user.api.UserSettingsApi;
import com.tydic.nbchat.user.api.bo.NbchatUserInfo;
import com.tydic.nbchat.user.api.bo.eums.JoinTenantType;
import com.tydic.nbchat.user.api.bo.mq.UserRegistContext;
import com.tydic.nbchat.user.api.bo.regist.UserRegistReqBO;
import com.tydic.nbchat.user.api.bo.user.UserBaseInfo;
import com.tydic.nicc.common.bo.user.SysRole;
import com.tydic.nicc.common.nbchat.constants.UserRoleConstants;
import com.tydic.nicc.common.nbchat.emus.UserRoleType;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.boot.starter.util.BaseRspUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.*;

@Slf4j
@Service
public class StudentManagementImpl implements StudentManagementApi {

    @Resource
    private NbchatSysUserTenantMapper nbchatSysUserTenantMapper;
    @Resource
    private SysPostMapper sysPostMapper;

    @DubboReference(version = "${nicc-dc-config.dubbo-provider.version}",
                    group = "${nicc-dc-config.dubbo-provider.group}", timeout = 100000)
    private NbchatUserRegLoginApi nbchatUserRegLoginApi;

    @DubboReference(version = "${nicc-dc-config.dubbo-provider.version}",
                    group = "${nicc-dc-config.dubbo-provider.group}", timeout = 10000)
    private UserBaseInfoApi userBaseInfoApi;

    @DubboReference(version = "${nicc-dc-config.dubbo-provider.version}",
                    group = "${nicc-dc-config.dubbo-provider.group}", timeout = 10000)
    private NbchatUserApi nbchatUserApi;

    @DubboReference(version = "${nicc-dc-config.dubbo-provider.version}",
                    group = "${nicc-dc-config.dubbo-provider.group}", timeout = 5000)
    private UserSettingsApi userSettingsApi;

    private final SysUserRoleBusiService sysUserRoleBusiService;
    private final SysTenantUserBuisService sysTenantUserBuisService;
    private final NbchatAdminConfigProperties nbchatAdminConfigProperties;
    private final RpUserDetailBusiService rpUserDetailBusiService;

    public StudentManagementImpl(SysUserRoleBusiService sysUserRoleBusiService,
                                 SysTenantUserBuisService sysTenantUserBuisService,
                                 NbchatAdminConfigProperties nbchatAdminConfigProperties,
                                 RpUserDetailBusiService rpUserDetailBusiService) {
        this.sysUserRoleBusiService = sysUserRoleBusiService;
        this.sysTenantUserBuisService = sysTenantUserBuisService;
        this.nbchatAdminConfigProperties = nbchatAdminConfigProperties;
        this.rpUserDetailBusiService = rpUserDetailBusiService;
    }

    /**
     * 新增学员
     *
     * @param @param studentManagementReqBO 学生管理要求博
     * @return @return {@link Rsp }
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Rsp addNewStudent(SysDeptUserSaveReqBO reqBO) {
        log.info("新增学员-开始: {}", reqBO);
        String targetTenant = reqBO.getTenantCode();
        Rsp<NbchatUserInfo> rsp = null;
        if (StringUtils.isAllBlank(reqBO.getPhone(), reqBO.getTargetTenantCode())) {
            rsp = nbchatUserApi.getUserInfo(reqBO.getUserId());
        } else {
            if (reqBO.hasRole(UserRoleConstants.sysAdmin)) {
                targetTenant = reqBO.getTargetTenantCode();
            }
            rsp = nbchatUserApi.getUserByPhone(reqBO.getPhone());
        }
        NbchatUserInfo userInfo;
        if (rsp.isSuccess()) {
            //手机号存在，只新增用户-租户关系
            userInfo = rsp.getData();
            log.info("新增学员-手机号已存在: {}", userInfo);
        } else {
            sysTenantUserBuisService.checkTenantCodeStatus(targetTenant);
            //手机号不存在，注册新用户
            UserRegistReqBO registReqBO = new UserRegistReqBO();
            registReqBO.setUsername(reqBO.getPhone());
            registReqBO.setPassword(reqBO.getPassword());
            if (StringUtils.isBlank(reqBO.getPassword())) {
                String prefix = nbchatAdminConfigProperties.getDefaultPasswordPrefix();
                registReqBO.setPassword(prefix);
                if (reqBO.getPhone().length() > 4) {
                    registReqBO.setPassword(prefix + reqBO.getPhone().substring(reqBO.getPhone().length() - 4));
                }
            }
            registReqBO.setPhone(reqBO.getPhone());
            registReqBO.setTenantCode(targetTenant);
            registReqBO.setUserRealityName(reqBO.getUserRealityName());
            registReqBO.setJoinType(JoinTenantType.ARTIFICIAL.getCode());
            registReqBO.setClientUa(reqBO.getClientUa());
            registReqBO.setClientIp(reqBO.getClientIp());
            log.info("新增学员-手机号不存在-注册新用户-注册信息:{}", registReqBO);
            rsp = nbchatUserRegLoginApi.regist(registReqBO);
            if (!rsp.isSuccess()) {
                log.error("新增学员-手机号不存在-注册新用户-失败: {}", rsp);
                return rsp;
            }
            userInfo = rsp.getData();
        }
        SysTenantOptUserReqBO bindUserReqBO = getSysTenantOptUserReqBO(reqBO, userInfo, targetTenant);
        Rsp addRsp = sysTenantUserBuisService.addUsers(bindUserReqBO);
        log.info("新增学员-新增用户-租户关系-结果:{}", addRsp);
        //更新角色
        sysUserRoleBusiService.updateRoles(targetTenant, userInfo.getUserId(), reqBO.getRole());
        //清除用户缓存
        userSettingsApi.removeUserCache(targetTenant, userInfo.getUserId());
        return rsp;
    }

    private static @NotNull SysTenantOptUserReqBO getSysTenantOptUserReqBO(SysDeptUserSaveReqBO reqBO,
                                                                           NbchatUserInfo userInfo,
                                                                           String targetTenant) {
        SysTenantUserInfoBO userInfoBO = new SysTenantUserInfoBO();
        BeanUtils.copyProperties(reqBO, userInfoBO);
        userInfoBO.setUserId(userInfo.getUserId());
        userInfoBO.setBirthday(reqBO.getBirthday());
        //添加新用户
        SysTenantOptUserReqBO bindUserReqBO = new SysTenantOptUserReqBO();
        bindUserReqBO.setDeptId(reqBO.getDeptId());
        bindUserReqBO.setUserInfos(Collections.singletonList(userInfoBO));
        bindUserReqBO.setTargetTenant(targetTenant);
        bindUserReqBO.setJoinType(JoinTenantType.ARTIFICIAL.getCode());
        return bindUserReqBO;
    }

    @Override
    public Rsp getUserInfoQR(SysDeptUserQueryReqBO reqBO) {
        log.info("查询学员信息: {}", reqBO);
        SysDeptUserQueryRspBO rspBO = new SysDeptUserQueryRspBO();
        UserBaseInfo userInfo;
        if (StringUtils.isNotBlank(reqBO.getTargetUid())) {
            userInfo = userBaseInfoApi.getByUserId(reqBO.getTargetTenantCode(), reqBO.getTargetUid()).getData();
            rspBO.setPhone(userInfo.getPhone());
            rspBO.setAvatar(userInfo.getAvatarUrl());
            rspBO.setUserRealityName(userInfo.getRealName());
        } else {
            NbchatSysUserTenant sysUserTenant = nbchatSysUserTenantMapper.selectByPrimaryKey(reqBO.getId());
            if (sysUserTenant == null) {
                return BaseRspUtils.createSuccessRsp("不存在该学员信息");
            }
            BeanUtils.copyProperties(sysUserTenant, rspBO);
            //查询用户信息
            userInfo = userBaseInfoApi.getByUserId(sysUserTenant.getTenantCode(),
                                                   sysUserTenant.getUserId()).getData();
        }
        if (userInfo == null) {
            log.error("查询学员信息-查询用户信息失败: {}", reqBO);
            return BaseRspUtils.createErrorRsp("查询用户信息失败");
        }
        rspBO.setPhone(userInfo.getPhone());
        //查询学员角色
        Set<SysRole> roles = new LinkedHashSet<>();
        if (userInfo.getRoleList() != null) {
            userInfo.getRoleList().forEach(role -> {
                SysRole sysRole = new SysRole();
                sysRole.setRoleCode(role.getRole());
                sysRole.setRoleName(role.getName());
                roles.add(sysRole);
            });
        }
        rspBO.setRoles(roles);
        rspBO.setDeptId(userInfo.getDept().getDeptId());
        rspBO.setDeptName(userInfo.getDept().getDeptName());
        rspBO.setPostList(userInfo.getPostList());
        log.debug("查询学员信息-查询用户信息成功:{}", rspBO);
        return BaseRspUtils.createSuccessRsp(rspBO);
    }

    @Override
    public Rsp getUserInfoQR(String tenantCode, String userId) {
        NbchatSysUserTenant sysUserTenant = nbchatSysUserTenantMapper.selectByUserIdAndTenantCode(userId, tenantCode);
        if (sysUserTenant == null) {
            return BaseRspUtils.createErrorRsp("查询失败");
        }
        //查询用户信息
        SysDeptUserQueryReqBO reqBO = new SysDeptUserQueryReqBO();
        reqBO.setTargetTenantCode(tenantCode);
        //reqBO.setTargetUid(userId);
        reqBO.setUserId(userId);
        reqBO.setTenantCode(tenantCode);
        reqBO.setId(sysUserTenant.getId());
        return getUserInfoQR(reqBO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Rsp importUsers(MultipartFile file, String tenantCode, String clientUa, String clientIp) {

        List<ImportUserInfo> userInfoList;
        try {
            userInfoList = EasyExcel.read(file.getInputStream())
                    .head(ImportUserInfo.class) // 指定数据模型
                    .sheet()
                    .doReadSync();
        } catch (IOException e) {
            log.error("导入学员-读取文件失败:{}", e.getMessage());
            throw new RuntimeException(e);
        }

        List<String> errorList = new ArrayList<>();
        userInfoList.forEach(userInfo -> {
            String phone = userInfo.getPhone();
            String role = userInfo.getRole();
            String deptId = userInfo.getDeptId();
            String postIdName = userInfo.getPostId();
            Integer postId = 0;
            if (postIdName.matches("\\d+")) {
                postId = Integer.parseInt(postIdName);
            } else {
                postId = sysPostMapper.selectPostIdByPostName(postIdName, tenantCode);
            }
            Boolean exists = nbchatSysUserTenantMapper.checkIfExists(role, deptId, postId);

            if (!exists) {
                List<Integer> postIdList = new ArrayList<>();
                postIdList.add(postId);
                HashSet<String> roleSet = new HashSet<>();
                roleSet.add(UserRoleType.getCodeByName(role));
                SysDeptUserSaveReqBO reqBO = new SysDeptUserSaveReqBO();
                reqBO.setPhone(phone);
                reqBO.setUserRealityName(userInfo.getUserRealityName());
                reqBO.setTenantCode(tenantCode);
                reqBO.setTargetTenantCode(tenantCode);
                reqBO.setJoinType(JoinTenantType.IMPORTS.getCode());
                reqBO.setPassword(nbchatAdminConfigProperties.getDefaultPasswordPrefix() + phone.substring(phone.length() - 4));
                reqBO.setDeptId(deptId);
                reqBO.setPostIds(postIdList);
                reqBO.setRole(roleSet);
                reqBO.setClientUa(clientUa);
                reqBO.setClientIp(clientIp);
                Rsp rsp = addNewStudent(reqBO);
                if (!rsp.isSuccess()) {
                    log.error("导入学员-用户信息失败:{}", rsp);
                    errorList.add(userInfo.getUserRealityName());
                }
            } else {
                log.info("导入学员-用户信息错误:{}", userInfo);
                errorList.add(userInfo.getUserRealityName());
            }

        });
        return BaseRspUtils.createSuccessRsp(errorList, "导入成功");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Rsp syncUserDetail() {
        List<NbchatSysUserTenant> nbchatSysUserTenantList = nbchatSysUserTenantMapper.selectNotSyncUserInfo();
        nbchatSysUserTenantList.forEach(sysUserTenant -> {
            UserRegistContext context = new UserRegistContext();
            BeanUtils.copyProperties(sysUserTenant, context);
            context.setCreatedTime(sysUserTenant.getCreateTime());
            context.setPhone(nbchatSysUserTenantMapper.getPhone(sysUserTenant.getUserId()));
            rpUserDetailBusiService.saveRpUserDetail(context);
        });
        log.info("同步用户详情成功:{}", nbchatSysUserTenantList.size());
        return BaseRspUtils.createSuccessRsp("同步用户详情成功");
    }

}
