package com.tydic.nbchat.admin.core.consumer;

import com.tydic.nbchat.admin.api.bo.SysTenantOptUserReqBO;
import com.tydic.nbchat.admin.api.bo.SysTenantUserInfoBO;
import com.tydic.nbchat.admin.core.busi.RpUserDetailBusiService;
import com.tydic.nbchat.admin.core.busi.SysTenantUserBuisService;
import com.tydic.nbchat.user.api.bo.constants.UserAttributeConstants;
import com.tydic.nbchat.user.api.bo.eums.JoinTenantType;
import com.tydic.nbchat.user.api.bo.mq.UserRegistContext;
import com.tydic.nicc.common.nbchat.constants.NbchatTopicsConstants;
import com.tydic.nicc.common.nbchat.msg.RebateInviteMsgContext;
import com.tydic.nicc.common.nbchat.msg.UserOperateLogContext;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.mq.starter.annotation.KKMqConsumer;
import com.tydic.nicc.mq.starter.api.KKMqConsumerListener;
import com.tydic.nicc.mq.starter.api.KKMqProducerHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

import static com.tydic.nicc.common.nbchat.constants.NbchatTopicsConstants.NBCHAT_REBATE_INVITE_TOPIC;
import static com.tydic.nicc.common.nbchat.constants.NbchatTopicsConstants.NBCHAT_USER_OPERATE_LOG_TOPIC;
import static com.tydic.nicc.common.nbchat.emus.RebateInviteEnum.INVITE_REG;
import static com.tydic.nicc.common.nbchat.emus.UserOperationType.REGISTER;

@Slf4j
@Component
@KKMqConsumer(consumerGroup = NbchatTopicsConstants.NBCHAT_USER_REGIST_CID, topic = NbchatTopicsConstants.NBCHAT_USER_REGIST_TOPIC)
public class UserRegistConsumer implements KKMqConsumerListener<UserRegistContext> {

    @Value("${rebate.enable:true}")
    private boolean rebateEnable;

    private final KKMqProducerHelper kkMqProducerHelper;
    private final RpUserDetailBusiService rpUserDetailBusiService;
    private final SysTenantUserBuisService sysTenantUserBuisService;

    public UserRegistConsumer(KKMqProducerHelper kkMqProducerHelper,
                              RpUserDetailBusiService rpUserDetailBusiService,
                              SysTenantUserBuisService sysTenantUserBuisService) {
        this.kkMqProducerHelper = kkMqProducerHelper;
        this.rpUserDetailBusiService = rpUserDetailBusiService;
        this.sysTenantUserBuisService = sysTenantUserBuisService;
    }

    @Override
    public void onMessage(UserRegistContext registContext) {
        log.info("用户注册通知: {}", registContext);
        SysTenantOptUserReqBO tenantOptUserReqBO = new SysTenantOptUserReqBO();
        tenantOptUserReqBO.setTargetTenant(registContext.getTenantCode());
        tenantOptUserReqBO.setJoinType(registContext.getJoinType());
        SysTenantUserInfoBO userInfoBO = new SysTenantUserInfoBO();
        userInfoBO.setUserId(registContext.getUserId());
        userInfoBO.setUserRealityName(registContext.getUserRealityName());
        tenantOptUserReqBO.setUserInfos(Collections.singletonList(userInfoBO));
        if (!JoinTenantType.ARTIFICIAL.getCode().equals(registContext.getJoinType())) {
            //人工创建的不需要重复加入租户关系
            Rsp rsp = sysTenantUserBuisService.addUsers(tenantOptUserReqBO);
            log.info("用户注册通知-加入租户: {}", rsp);
        }
        try {
            log.info("用户注册通知-初始化报表明细: {}", registContext);
            rpUserDetailBusiService.saveRpUserDetail(registContext);
            if (!UserAttributeConstants.DEFAULT_TENANT_CODE.equals(registContext.getTenantCode())) {
                //尝试加入到默认租户
                boolean join = sysTenantUserBuisService.addDefaultTenant(userInfoBO, registContext.getJoinType());
                if (join) {
                    registContext.setTenantCode(UserAttributeConstants.DEFAULT_TENANT_CODE);
                    log.info("用户注册通知-加入默认租户: {}", registContext);
                    rpUserDetailBusiService.saveRpUserDetail(registContext);
                }
            }
            // 发送用户操作日志
            UserOperateLogContext userOperateLogContext = UserOperateLogContext.build(REGISTER, registContext.getTenantCode(), registContext.getUserId(), registContext.getClientUa(), registContext.getClientIp());
            try {
                log.info("发送用户操作日志事件消息: {}", userOperateLogContext);
                kkMqProducerHelper.sendMsg(NBCHAT_USER_OPERATE_LOG_TOPIC, userOperateLogContext);
            } catch (Exception e) {
                log.error("发送用户操作日志事件消息-异常:{}", userOperateLogContext, e);
            }
            // 如果要请拉新开关开启，且当前用户存在邀请码，则发送消息通知返利接口发送优惠券
            if (rebateEnable && StringUtils.isNotBlank(registContext.getInviteCode())) {
                // 发送邀请拉新消息
                RebateInviteMsgContext context = new RebateInviteMsgContext();
                context.setEventType(INVITE_REG.getCode());
                context.setEventTime(new Date());
                context.setTenantCode(registContext.getTenantCode());
                context.setUserId(registContext.getUserId());
                Map<String, Object> extParams = new HashMap<>();
                extParams.put("inviteCode", registContext.getInviteCode());
                context.setExtParams(extParams);
                log.info("发送邀请用户注册事件消息: {}", context);
                kkMqProducerHelper.sendMsg(NBCHAT_REBATE_INVITE_TOPIC, context);
            }
        } catch (Exception e) {
            log.error("用户注册通知-初始化报表明细异常: {}", registContext, e);
        }
    }
}
