package com.tydic.nbchat.admin.core.config.properties;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

@Data
@Component
@ConfigurationProperties(prefix = "nbchat-admin.config.qihu")
public class QihuConfigProperties {
    /**
     * 360点睛平台App-Key
     */
    private String appKey;
    
    /**
     * 360点睛平台App-Secret
     */
    private String appSecret;
    
    /**
     * 360点睛平台基础URL
     */
    private String baseUrl = "https://convert.dop.360.cn";
}
