package com.tydic.nbchat.admin.core.service.impl;

import com.alibaba.nacos.common.utils.CollectionUtils;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.tydic.nbchat.admin.api.SysUpdateLogApi;
import com.tydic.nbchat.admin.api.bo.SysUpdateLogQueryReqBo;
import com.tydic.nbchat.admin.api.bo.SysUpdateLogRspBo;
import com.tydic.nbchat.admin.mapper.SysUpdateLogMapper;
import com.tydic.nbchat.admin.mapper.po.SysUpdateLog;
import com.tydic.nbchat.admin.mapper.po.SysUpdateLogCondition;
import com.tydic.nbchat.user.api.bo.constants.RedisConstants;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.base.bo.RspList;
import com.tydic.nicc.dc.boot.starter.redis.RedisHelper;
import com.tydic.nicc.dc.boot.starter.util.BaseRspUtils;
import com.tydic.nicc.framework.utils.NiccCommonUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;

@Slf4j
@Service
public class SysUpdateLogServiceImpl implements SysUpdateLogApi {
    @Resource
    private SysUpdateLogMapper sysUpdateLogMapper;
    @Resource
    private RedisHelper redisHelper;
    @Override
    public RspList getSysUpdateLogList(SysUpdateLogQueryReqBo reqBO) {
        log.info("查询系统日志:{}", reqBO);
        SysUpdateLogCondition condition = new SysUpdateLogCondition();
        BeanUtils.copyProperties(reqBO, condition);
        if (reqBO.getVersionDateList() != null && !reqBO.getVersionDateList().isEmpty()) {
            condition.setVersionDateList(reqBO.getVersionDateList());
        }
        Page<SysUpdateLog> page = PageHelper.startPage(reqBO.getPage(), reqBO.getLimit());
        List<SysUpdateLog> result = sysUpdateLogMapper.selectByCondition(condition);
        List<SysUpdateLogRspBo> list = new ArrayList<>();
        NiccCommonUtil.copyList(result, list, SysUpdateLogRspBo.class);
        return BaseRspUtils.createSuccessRspList(list, page.getTotal());
    }

    /**
     * 新增或修改更新日志内容
     * @param reqBO
     * @return
     */
    @Override
    public Rsp saveOrUpdateSysUpdateLog(SysUpdateLogQueryReqBo reqBO) {
        log.info("新增或修改更新日志内容: {}", reqBO);
        SysUpdateLog sysUpdateLog = new SysUpdateLog();
        BeanUtils.copyProperties(reqBO, sysUpdateLog);

        // 如果状态为 0，设置 publishTime 为 null
        if ("0".equals(reqBO.getStatus())) {
            sysUpdateLog.setPublishTime(null);
        }

        if (reqBO.getId() == null) {
            // 新增操作
            sysUpdateLog.setCreateTime(new Date());
            sysUpdateLog.setIsValid("1");
            sysUpdateLog.setStatus("0");  // 默认新增为草稿
            sysUpdateLog.setPublishTime(null); // 显式置空发布时间
            sysUpdateLogMapper.insert(sysUpdateLog);
        } else {
            // 更新操作
            sysUpdateLog.setUpdateTime(new Date());
            if (!"0".equals(reqBO.getStatus())) {
                // 非草稿状态才设置发布时间
                sysUpdateLog.setPublishTime(new Date());
            }
            sysUpdateLogMapper.updateByPrimaryKeySelective(sysUpdateLog);
        }
        // 获取最新的 version_date 并更新 Redis
        String latestVersion = sysUpdateLogMapper.getLatestVersionDate();
        if (latestVersion != null) {
            redisHelper.set(RedisConstants.CURRENT_VERSION_DATE, latestVersion);
        }

        return BaseRspUtils.createSuccessRsp("操作成功");
    }



    /**
     * 点击量
     */
    @Override
    public Rsp clickSysUpdateLog(SysUpdateLogQueryReqBo reqBO) {
        log.info("更新日志点击量统计:{}", reqBO);
        if (reqBO.getId() == null) {
            return BaseRspUtils.createErrorRsp("ID不能为空");
        }

        String clickKey = RedisConstants.CLICK_COUNT_PREFIX + reqBO.getId();
        // Redis计数自增
        Long clickCount = redisHelper.incr(clickKey, 1L);
        // 每累计10次点击同步一次数据库
        if (clickCount % 10 == 0) {
            SysUpdateLog sysUpdateLog = new SysUpdateLog();
            sysUpdateLog.setId(reqBO.getId());
            sysUpdateLog.setClickCount(clickCount.intValue());
            sysUpdateLog.setUpdateTime(new Date());
            sysUpdateLogMapper.updateByPrimaryKeySelective(sysUpdateLog);
        }
        return BaseRspUtils.createSuccessRsp("点击量更新成功");
    }

    /**
     * 浏览量
     */
    @Override
    public Rsp browseSysUpdateLog(SysUpdateLogQueryReqBo reqBO) {
        log.info("更新日志浏览量统计:{}", reqBO);
        if (reqBO.getId() == null) {
            return BaseRspUtils.createErrorRsp("ID不能为空");
        }
        String browseKey = RedisConstants.VIEW_COUNT_PREFIX + reqBO.getId();
        // Redis计数自增
        Long browseCount = redisHelper.incr(browseKey, 1L);
        // 每累计10次点击同步一次数据库
        if (browseCount % 10 == 0) {
            SysUpdateLog sysUpdateLog = new SysUpdateLog();
            sysUpdateLog.setId(reqBO.getId());
            sysUpdateLog.setViewCount(browseCount.intValue());
            sysUpdateLog.setUpdateTime(new Date());
            sysUpdateLogMapper.updateByPrimaryKeySelective(sysUpdateLog);
        }
        return BaseRspUtils.createSuccessRsp("浏览量更新成功");
    }

    /**
     * 用户端获取系统更新日志列表
     *
     * @param reqBO
     * @return
     */
    @Override
    public Rsp getSysUpdateLog(SysUpdateLogQueryReqBo reqBO) {
        log.info("用户端获取最新系统更新日志列表:{}", reqBO);
        if (reqBO.getUserId() == null) {
            return BaseRspUtils.createErrorRsp("用户ID不能为空");
        }

        // 获取当前最新版本号
        String currentLatestVersion = (String) redisHelper.get(RedisConstants.CURRENT_VERSION_DATE);
        if (currentLatestVersion == null) {
            // 如果 Redis 中没有缓存版本号，则从数据库获取并写入
            List<SysUpdateLog> logs = sysUpdateLogMapper.selectNewVersion(new SysUpdateLogCondition());
            if (CollectionUtils.isEmpty(logs)) {
                return BaseRspUtils.createSuccessRsp(Collections.singletonMap("flag", false));
            }
            currentLatestVersion = logs.get(0).getVersionDate();
            redisHelper.set(RedisConstants.CURRENT_VERSION_DATE, currentLatestVersion);
        }
        // 构造 Redis Key 判断用户是否已查看
        String userViewKey = RedisConstants.USER_VIEW_CURRENT_VERSION + reqBO.getUserId() + ":" + currentLatestVersion;
        Object viewed = redisHelper.get(userViewKey);
        if ("1".equals(viewed)) {
            return BaseRspUtils.createSuccessRsp(Collections.singletonMap("flag", false));
        }

        // 查询日志数据
        SysUpdateLogCondition condition = new SysUpdateLogCondition();
        BeanUtils.copyProperties(reqBO, condition);
        List<SysUpdateLog> result = sysUpdateLogMapper.selectNewVersion(condition);

        if (CollectionUtils.isEmpty(result)) {
            return BaseRspUtils.createSuccessRsp(Collections.singletonMap("flag", false));
        }

        // 转换响应数据
        List<SysUpdateLogRspBo> list = new ArrayList<>();
        NiccCommonUtil.copyList(result, list, SysUpdateLogRspBo.class);

        Map<String, Object> responseMap = new HashMap<>();
        responseMap.put("flag", true);
        responseMap.put("data", list);

        // 设置缓存过期时间为一个月
        redisHelper.set(userViewKey, "1", 30 * 24 * 60 * 60);

        return BaseRspUtils.createSuccessRsp(responseMap);
    }



    @Override
    public Rsp deleteSysUpdateLog(SysUpdateLogQueryReqBo reqBO) {
        log.info("删除系统日志:{}", reqBO);
        // 检查是否传入了必要的 ID
        if (reqBO.getId() == null) {
            return BaseRspUtils.createErrorRsp("ID不能为空");
        }
        List<Integer> idList = Collections.singletonList(reqBO.getId());
        int deletedCount = sysUpdateLogMapper.deleteByPrimaryKeyIn(idList);
        if (deletedCount > 0) {
            return BaseRspUtils.createSuccessRsp("删除成功");
        } else {
            return BaseRspUtils.createErrorRsp("删除失败，记录不存在或已被删除");
        }
    }

    @Override
    public Rsp querySysUpdateLog(SysUpdateLogQueryReqBo reqBO) {
        log.info("查询系统更新日志: {}", reqBO);
        // 校验参数
        if (reqBO == null || reqBO.getId() == null) {
            return BaseRspUtils.createErrorRsp("ID不能为空");
        }

        SysUpdateLog logEntry = sysUpdateLogMapper.selectByPrimaryKey(reqBO.getId());

        if (logEntry != null) {
            return BaseRspUtils.createSuccessRsp(logEntry);
        } else {
            return BaseRspUtils.createErrorRsp("未找到对应的日志记录");
        }
    }

    @Override
    public Rsp querySysUpdateVersiontree() {
        // 查询全部 version_date 日期（不加筛选）
        List<String> allDates = sysUpdateLogMapper.selectAllVersionDates();
        Set<String> dateSet = new HashSet<>(allDates); // 去重
        log.info("version_date Set: {}", dateSet);

        // 构建层级结构 map：年 -> 月 -> 日（全部降序）
        Map<String, Map<String, List<String>>> versionMap = new TreeMap<>(Comparator.reverseOrder());
        for (String fullDate : dateSet) {
            if (!fullDate.matches("\\d{4}-\\d{2}-\\d{2}")) continue;

            String year = fullDate.substring(0, 4);
            String month = fullDate.substring(0, 7);  // yyyy-MM

            versionMap
                    .computeIfAbsent(year, k -> new TreeMap<>(Comparator.reverseOrder()))
                    .computeIfAbsent(month, k -> new ArrayList<>())
                    .add(fullDate);
        }

        // 构建目标 JSON 树结构
        List<Map<String, Object>> result = new ArrayList<>();
        for (Map.Entry<String, Map<String, List<String>>> yearEntry : versionMap.entrySet()) {
            String year = yearEntry.getKey();
            Map<String, Object> yearNode = new HashMap<>();
            yearNode.put("title", year + "年");
            yearNode.put("value", year);
            yearNode.put("key", year);

            List<Map<String, Object>> monthList = new ArrayList<>();
            for (Map.Entry<String, List<String>> monthEntry : yearEntry.getValue().entrySet()) {
                String month = monthEntry.getKey();
                String monthTitle = Integer.parseInt(month.substring(5)) + "月";

                Map<String, Object> monthNode = new HashMap<>();
                monthNode.put("title", monthTitle);
                monthNode.put("value", month);
                monthNode.put("key", month);

                // 日期降序排序
                List<String> days = monthEntry.getValue();
                days.sort(Comparator.reverseOrder());

                List<Map<String, Object>> dayList = new ArrayList<>();
                for (String day : days) {
                    String dayTitle = Integer.parseInt(day.substring(8)) + "日";

                    Map<String, Object> dayNode = new HashMap<>();
                    dayNode.put("title", dayTitle);
                    dayNode.put("value", day);
                    dayNode.put("key", day);
                    dayList.add(dayNode);
                }

                monthNode.put("children", dayList);
                monthList.add(monthNode);
            }

            yearNode.put("children", monthList);
            result.add(yearNode);
        }

        return BaseRspUtils.createSuccessRsp(result);
    }


}
