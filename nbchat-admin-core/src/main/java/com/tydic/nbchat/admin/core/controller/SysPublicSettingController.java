package com.tydic.nbchat.admin.core.controller;

import com.tydic.nbchat.admin.api.bo.PublicSettingQueryReqBO;
import com.tydic.nbchat.admin.api.bo.PublicSettingSaveReqBO;
import com.tydic.nicc.common.bo.premission.Logical;
import com.tydic.nicc.common.bo.premission.RequiresRole;
import com.tydic.nicc.common.nbchat.constants.UserRoleConstants;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.boot.starter.redis.RedisHelper;
import com.tydic.nicc.dc.boot.starter.util.BaseRspUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * 系统配置，不需要登录可查询
 */
@Slf4j
@RestController
@RequestMapping("/admin/public/setting")
public class SysPublicSettingController {

    private static final String SETTING_KEY = "nbchat-admin:setting:";
    @Resource
    private RedisHelper redisHelper;

    @RequiresRole(value = {UserRoleConstants.sysAdmin,
            UserRoleConstants.tenantAdmin}, logical = Logical.OR)
    @PostMapping("/save")
    public Rsp save(@RequestBody PublicSettingSaveReqBO reqBO) {
        log.info("保存公共设置: {}", reqBO);
        if (StringUtils.isBlank(reqBO.getTargetTenant())) {
            reqBO.setTargetTenant("public");
        }
        if (reqBO.getSettings() != null) {
            String key = SETTING_KEY + reqBO.getTargetTenant();
            boolean saved = redisHelper.hmset(key, reqBO.getSettings(), 0);
            return BaseRspUtils.createSuccessRsp(saved);
        }
        return BaseRspUtils.createErrorRsp("请求参数错误");
    }

    @PostMapping("/info")
    public Rsp info(@RequestBody PublicSettingQueryReqBO reqBO) {
        if (StringUtils.isBlank(reqBO.getTargetTenant())) {
            reqBO.setTargetTenant("public");
        }
        String key = SETTING_KEY + reqBO.getTargetTenant();
        Map<Object,Object> settings = redisHelper.hmget(key);
        return BaseRspUtils.createSuccessRsp(Objects.requireNonNullElseGet(settings, HashMap::new));
    }

}
