package com.tydic.nbchat.admin.core.service.impl;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.tydic.nbchat.admin.api.SysUpvoteRecordApi;
import com.tydic.nbchat.admin.api.bo.SysUpvoteRecordBO;
import com.tydic.nbchat.admin.api.bo.SysUpvoteRecordQueryReqBO;
import com.tydic.nbchat.admin.api.bo.SysUpvoteRecordQueryRspBO;
import com.tydic.nbchat.admin.api.bo.SysUpvoteRecordUpdateBO;
import com.tydic.nbchat.admin.api.bo.eum.ProductModuleEnum;
import com.tydic.nbchat.admin.mapper.SysUpvoteRecordMapper;
import com.tydic.nbchat.admin.mapper.SysUpvoteRecordReasonMapper;
import com.tydic.nbchat.admin.mapper.po.*;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.base.bo.RspList;
import com.tydic.nicc.dc.boot.starter.util.BaseRspUtils;
import com.tydic.nicc.framework.utils.NiccCommonUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @datetime：2024/9/19 10:16
 * @description:
 */
@Slf4j
@Service
public class SysUpvoteRecordServiceImpl implements SysUpvoteRecordApi {
    @Resource
    private SysUpvoteRecordMapper sysUpvoteRecordMapper;
    @Resource
    private SysUpvoteRecordReasonMapper sysUpvoteRecordReasonMapper;


    @Override
    public Rsp addNewUpvoteRecord(SysUpvoteRecordBO recordBO) {
        SysUpvoteRecord record = new SysUpvoteRecord();
        BeanUtils.copyProperties(recordBO, record);
        String productModule = recordBO.getProductModule();
        if (productModule != null && !productModule.trim().isEmpty()) {
            record.setProductModule(ProductModuleEnum.getDescByCode(recordBO.getProductModule()));
        } else {
            record.setProductModule(ProductModuleEnum.OTHER.getCode());
        }
        if(record.getId() == null){
            record.setCreateTime(new Date());
            sysUpvoteRecordMapper.insertSelective(record);
            insertRecordReason(record);
        }else {
            sysUpvoteRecordMapper.update(record);
            sysUpvoteRecordReasonMapper.deleteByUpvoteId(recordBO.getId());
            insertRecordReason(record);
        }
        return BaseRspUtils.createSuccessRsp("保存成功！");

    }

    /**
     * 批量写入评价原因
     * @param record
     */
    private  void insertRecordReason(SysUpvoteRecord record){
        if(!StringUtils.isEmpty(record.getUnlikeReason())) {
            String[] unlikeReasons = record.getUnlikeReason().split(",");
            List<SysUpvoteRecordReasonPO> recordReasonPOS = new ArrayList<>();
            for (String unlikeReason : unlikeReasons) {
                SysUpvoteRecordReasonPO recordReasonPO = new SysUpvoteRecordReasonPO();
                recordReasonPO.setReason(unlikeReason);
                recordReasonPO.setUpvoteId(record.getId());
                recordReasonPO.setCreateTime(new Date());
                recordReasonPOS.add(recordReasonPO);
            }
            sysUpvoteRecordReasonMapper.insertBatch(recordReasonPOS);
        }
    }

    @Override
    public Rsp getById(SysUpvoteRecordBO recordBO) {
        SysUpvoteRecord sysUpvoteRecord = sysUpvoteRecordMapper.queryById(recordBO.getId());
        SysUpvoteRecordBO bo = new SysUpvoteRecordBO();
        BeanUtils.copyProperties(sysUpvoteRecord, bo);
        return BaseRspUtils.createSuccessRsp(bo);
    }

    @Override
    public RspList getUpvoteRecordList(SysUpvoteRecordBO recordBO) {
        SysUpvoteRecord record = new SysUpvoteRecord();
        BeanUtils.copyProperties(recordBO, record);
        Page<SysMenuTpl> page = PageHelper.startPage(recordBO.getPage(), recordBO.getLimit());
        sysUpvoteRecordMapper.selectAll(record);
        List<SysUpvoteRecordBO> list = new ArrayList<>();
        NiccCommonUtil.copyList(page.getResult(), list, SysUpvoteRecordBO.class);
        return BaseRspUtils.createSuccessRspList(list, page.getTotal());
    }

    @Override
    public RspList getUpvoteRecordAdminList(SysUpvoteRecordQueryReqBO recordBO) {
        List<SysUpvoteRecordQueryRspBO> rspBOList = new ArrayList<>();
        SysUpvoteRecordCondition condition = new SysUpvoteRecordCondition();
        BeanUtils.copyProperties(recordBO, condition);
        Page<SysMenuTpl> page = PageHelper.startPage(recordBO.getPage(), recordBO.getLimit());
        List<SysUpvoteRecordDto> recordDtoList = new ArrayList<>();
        // 产品模块判断1 视频 ，2 ppt
        if(ProductModuleEnum.VIDEO_MAKE.getCode().equals(recordBO.getProductModule())){
            recordDtoList= sysUpvoteRecordMapper.selectAudioAdminAll(condition);
        }if(ProductModuleEnum.PPT_MAKE.getCode().equals(recordBO.getProductModule())){
            recordDtoList= sysUpvoteRecordMapper.selectPptAdminAll(condition);
        }else{
            log.info("查询列表-产品模块:{} ", recordBO.getProductModule());
        }
        if (CollectionUtils.isEmpty(recordDtoList)) {
            return BaseRspUtils.createSuccessRspList(rspBOList,0L);
        }
        NiccCommonUtil.copyList(recordDtoList,rspBOList,SysUpvoteRecordQueryRspBO.class);
        return BaseRspUtils.createSuccessRspList(rspBOList,page != null ? page.getTotal() : recordDtoList.size());
    }

    /**
     * 后台管理更新状态
     * @param recordBO
     * @return
     */
    public Rsp updateAdminState(SysUpvoteRecordUpdateBO recordBO) {
        if (recordBO.getId() == null) {
            return BaseRspUtils.createErrorRsp("ID不能为空");
        }
        if (StringUtils.isEmpty(recordBO.getProductModule())) {
            return BaseRspUtils.createErrorRsp("产品模块不能为空");
        }
        SysUpvoteRecord recordPo = sysUpvoteRecordMapper.selectByPO(recordBO.getId(), recordBO.getProductModule());
        if(!ObjectUtils.isEmpty(recordPo)){
            if(!"0".equals(recordPo.getUpvoteState())){
                return BaseRspUtils.createErrorRsp("当前评价记录已处理，不允许重复操作！");
            }
        }else {
            return BaseRspUtils.createErrorRsp("根据ID未找到评价记录！");
        }
        SysUpvoteRecord record = new SysUpvoteRecord();
        record.setUpvoteState(recordBO.getUpvoteState());
        record.setProductModule(recordBO.getProductModule());
        record.setId(recordPo.getId());
        record.setHandleTime(new Date());
        return BaseRspUtils.createSuccessRsp(sysUpvoteRecordMapper.updateState(record));
    }
}
