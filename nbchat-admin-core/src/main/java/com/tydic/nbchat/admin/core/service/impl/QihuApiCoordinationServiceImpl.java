package com.tydic.nbchat.admin.core.service.impl;

import com.tydic.nbchat.admin.api.qihu.QihuApiCoordinationService;
import com.tydic.nbchat.admin.api.bo.qihu.QihuApiCoordinationReqBO;
import com.tydic.nbchat.admin.api.bo.qihu.QihuConversionDataBO;
import com.tydic.nbchat.admin.core.config.properties.QihuConfigProperties;
import com.tydic.nbchat.admin.core.utils.QihuSendConvertDataUtil;
import com.tydic.nicc.dc.boot.starter.util.BaseRspUtils;
import com.tydic.nicc.dc.base.bo.Rsp;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Date;

@Slf4j
@Service
@AllArgsConstructor
public class QihuApiCoordinationServiceImpl implements QihuApiCoordinationService {

    private QihuConfigProperties qihuConfigProperties;

    @Override
    public Rsp dataConversionCallback(QihuApiCoordinationReqBO reqBo) {
        try {
            String baseUrl = qihuConfigProperties.getBaseUrl();
            String appKey = qihuConfigProperties.getAppKey();
            String appSecret = qihuConfigProperties.getAppSecret();
            
            // 参数校验
            if (baseUrl == null || appKey == null || appSecret == null) {
                log.error("360配置参数不完整: baseUrl={}, appKey={}, appSecret={}", baseUrl, appKey, appSecret != null ? "***" : null);
                return BaseRspUtils.createErrorRsp("360配置参数不完整");
            }
            
            QihuConversionDataBO conversionData = reqBo.getData();
            if (conversionData == null) {
                log.error("360转化数据为空");
                return BaseRspUtils.createErrorRsp("转化数据不能为空");
            }
            conversionData.setRequestTime(new Date().getTime());
            // 发送转化数据
            Boolean isSend = QihuSendConvertDataUtil.sendConvertData(baseUrl, appKey, appSecret, conversionData);
            
            if (isSend) {
                log.info("360转化数据回传成功");
                return BaseRspUtils.createSuccessRsp("360回传数据成功");
            } else {
                log.error("360转化数据回传失败");
                return BaseRspUtils.createErrorRsp("360回传数据失败");
            }
            
        } catch (Exception e) {
            log.error("360转化数据回传异常", e);
            return BaseRspUtils.createErrorRsp("360回传数据异常: " + e.getMessage());
        }
    }
}
