package com.tydic.nbchat.admin.core.controller;

import com.alibaba.fastjson.JSON;
import com.tydic.nbchat.admin.api.SysShortLinkApi;
import com.tydic.nbchat.admin.api.bo.sentence.ShortLinkReqBO;
import com.tydic.nbchat.admin.core.utils.IPUtils;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.boot.starter.util.BaseRspUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * <AUTHOR>
 * @create 2025/6/9
 * @since 1.0.0
 **/
@Slf4j
@RestController
@RequestMapping("/admin")
public class SysShortLinkController {


    @Resource
    private SysShortLinkApi sysShortLinkApi;

    /**
     * 生成分享视频短链接
     */
    @PostMapping("/shortLinkShare/getLinkUrl")
    public Rsp getShortLinkUrl(@RequestBody ShortLinkReqBO req){
        log.info("生成分享视频短链接分享码: {}", JSON.toJSONString(req));
        if(StringUtils.isBlank(req.getBizId()) || StringUtils.isBlank(req.getBizType())){
            return BaseRspUtils.createErrorRsp("链接入参异常");
        }
        return sysShortLinkApi.getShortLinkUrl(req);
    }

    /**
     * 根据分享码重定向跳转到实际地址
     **/
    @GetMapping("/s/{shareCode}")
    public Rsp videoShare(@PathVariable String shareCode, HttpServletRequest request, HttpServletResponse response){
        try {
            Rsp rsp = sysShortLinkApi.queryShareUrlByCode(shareCode);
            if(!rsp.isSuccess()){
                return rsp;
            }
            String url = rsp.getData().toString();
            log.info("短链接分享重定向分享码shareKey{}，跳转地址:", shareCode,url);
            //记录短链接访问记录
            String userAgent = request.getHeader("User-Agent");
            String clientIp = IPUtils.getIpAddr(request);
            sysShortLinkApi.saveShortLinkVisitData(shareCode,clientIp !=null ? clientIp : "", userAgent != null ? userAgent : "");
            //重定向响应
            response.sendRedirect(url);
            return null;
        } catch (Exception e) {
            log.error("视频分享重定向异常",e);
            return BaseRspUtils.createSuccessRsp("获取视频地址异常");
        }
    }


}
