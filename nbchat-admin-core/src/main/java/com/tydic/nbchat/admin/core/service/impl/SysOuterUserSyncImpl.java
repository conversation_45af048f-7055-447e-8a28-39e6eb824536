package com.tydic.nbchat.admin.core.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.tydic.nbchat.admin.api.SysOuterUserSyncApi;
import com.tydic.nbchat.admin.api.SysTokenConfigApi;
import com.tydic.nbchat.admin.api.bo.api_token.SysApiTokenCreateReqBO;
import com.tydic.nbchat.admin.api.bo.api_token.SysApiTokenRefreshReqBO;
import com.tydic.nbchat.admin.api.bo.api_token.SysApiTokenRefreshRspBO;
import com.tydic.nbchat.admin.api.bo.outer_user.OuterUserAccessTokenReqBO;
import com.tydic.nbchat.admin.api.bo.outer_user.OuterUserSyncReqBO;
import com.tydic.nbchat.admin.api.bo.outer_user.OuterUserSyncRspBO;
import com.tydic.nbchat.admin.api.bo.permission.ApiConfigPermission;
import com.tydic.nbchat.admin.mapper.NbchatSysTenantMapper;
import com.tydic.nbchat.admin.mapper.SysApiTokenConfigMapper;
import com.tydic.nbchat.admin.mapper.po.NbchatSysTenant;
import com.tydic.nbchat.admin.mapper.po.SysApiTokenConfig;
import com.tydic.nbchat.user.api.NbchatUserApi;
import com.tydic.nbchat.user.api.NbchatUserRegLoginApi;
import com.tydic.nbchat.user.api.UserManageService;
import com.tydic.nbchat.user.api.UserSettingsApi;
import com.tydic.nbchat.user.api.bo.NbchatUserInfo;
import com.tydic.nbchat.user.api.bo.constants.UserAttributeConstants;
import com.tydic.nbchat.user.api.bo.eums.JoinTenantType;
import com.tydic.nbchat.user.api.bo.manage.UpdateUserRequest;
import com.tydic.nbchat.user.api.bo.regist.UserRegistReqBO;
import com.tydic.nicc.common.eums.EntityValidType;
import com.tydic.nicc.dc.base.annotions.MethodParamVerifyEnable;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.boot.starter.util.BaseRspUtils;
import com.tydic.nicc.dc.boot.starter.util.IdWorker;
import com.tydic.nicc.framework.utils.NiccCommonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Slf4j
@Service
public class SysOuterUserSyncImpl implements SysOuterUserSyncApi {

    @DubboReference(version = "${nicc-dc-config.dubbo-provider.version}",
                    group = "${nicc-dc-config.dubbo-provider.group}", timeout = 5000)
    private NbchatUserRegLoginApi nbchatUserRegLoginApi;
    @DubboReference(version = "${nicc-dc-config.dubbo-provider.version}",
                    group = "${nicc-dc-config.dubbo-provider.group}", timeout = 3000)
    private NbchatUserApi nbchatUserApi;
    @DubboReference(version = "${nicc-dc-config.dubbo-provider.version}",
                    group = "${nicc-dc-config.dubbo-provider.group}", timeout = 100000)
    private UserManageService userManageService;
    @DubboReference(version = "${nicc-dc-config.dubbo-provider.version}",
                    group = "${nicc-dc-config.dubbo-provider.group}", timeout = 3000)
    private UserSettingsApi userSettingsApi;

    @Resource
    private NbchatSysTenantMapper nbchatSysTenantMapper;
    @Resource
    private SysApiTokenConfigMapper sysApiTokenConfigMapper;

    private final SysTokenConfigApi sysTokenConfigApi;

    public SysOuterUserSyncImpl(SysTokenConfigApi sysTokenConfigApi) {
        this.sysTokenConfigApi = sysTokenConfigApi;
    }

    @MethodParamVerifyEnable
    @Override
    public Rsp<OuterUserSyncRspBO> syncUser(OuterUserSyncReqBO sysUserSyncReqBO) {
        /**
         * 1. 校验该租户是否已授权
         * 2. 注册到公共租户，加入到企业租户
         * 3. 分配token
         */
        log.info("同步外部用户信息：{}", sysUserSyncReqBO);
        NbchatSysTenant tenant = nbchatSysTenantMapper.selectByPrimaryKey(sysUserSyncReqBO.getAppTenantCode());
        if (tenant == null || EntityValidType.DELETE.getCode().equals(tenant.getAuthApi())) {
            return BaseRspUtils.createErrorRsp("租户未授权");
        }

        NbchatUserInfo userInfo = null;
        if (StringUtils.isNotBlank(sysUserSyncReqBO.getPhone())) {
            Rsp<NbchatUserInfo> userRsp = nbchatUserApi.getUserByPhone(sysUserSyncReqBO.getPhone());
            if (userRsp.isSuccess()) {
                userInfo = userRsp.getData();
            } else {
                log.warn("同步外部用户信息-查询用户失败: {}|{}", sysUserSyncReqBO, userRsp);
            }
        }
        if (userInfo == null) {
            UserRegistReqBO regReq = new UserRegistReqBO();
            regReq.setTenantCode(sysUserSyncReqBO.getAppTenantCode());
            regReq.setUsername(sysUserSyncReqBO.getUsername());
            regReq.setPassword(sysUserSyncReqBO.getPassword());
            regReq.setName(sysUserSyncReqBO.getName());
            regReq.setUserRealityName(sysUserSyncReqBO.getUserRealityName());
            regReq.setPhone(sysUserSyncReqBO.getPhone());
            regReq.setJoinType(JoinTenantType.OUTER_API.getCode());
            regReq.setClientUa(sysUserSyncReqBO.getClientUa());
            regReq.setClientIp(sysUserSyncReqBO.getClientIp());
            //如果用户名和密码为空，默认生成
            if (StringUtils.isBlank(regReq.getUsername())) {
                regReq.setUsername(IdWorker.nextAutoIdStr());
            }
            if (StringUtils.isBlank(regReq.getPassword())) {
                regReq.setPassword(String.valueOf(NiccCommonUtil.generateRandomCode(8, 11)));
            }
            Rsp<NbchatUserInfo> userRegRsp = nbchatUserRegLoginApi.regist(regReq);
            if (userRegRsp.isSuccess()) {
                userInfo = userRegRsp.getData();
                try {
                    userSettingsApi.removeUserCache(userInfo.getTenantCode(), userInfo.getUserId());
                } catch (Exception e) {
                    log.error("同步外部用户信息-清除用户缓存失败: {}", userInfo, e);
                }
            } else {
                log.error("同步外部用户信息-用户注册失败: {}|{}", regReq, userRegRsp);
                return BaseRspUtils.createErrorRsp(userRegRsp.getRspDesc());
            }
        } else {
            /*int count = nbchatSysTenantMapper.checkUserInTenant(sysUserSyncReqBO.getAppTenantCode(), userInfo.getUserId());
            if (count == 0) {
                return BaseRspUtils.createErrorRsp("用户同步失败: 非法请求!");
            }*/
            if (userInfo.getTenantCode().equals(UserAttributeConstants.DEFAULT_TENANT_CODE)) {
                //切换到企业租户
                try {
                    userInfo.setTenantCode(sysUserSyncReqBO.getAppTenantCode());
                    UpdateUserRequest updateUserRequest = new UpdateUserRequest();
                    updateUserRequest.setTargetUid(userInfo.getUserId());
                    updateUserRequest.setTenant(sysUserSyncReqBO.getAppTenantCode());
                    updateUserRequest.setUserId(userInfo.getUserId());
                    userManageService.updateInfo(updateUserRequest);
                } catch (Exception e) {
                    log.error("同步外部用户信息-切换租户失败: {}", userInfo, e);
                }
            }
        }
        Rsp<SysApiTokenRefreshRspBO> tokenRsp;
        if (userInfo != null) {
            // 刷新缓存
            String sk = geneSk(sysUserSyncReqBO.getAppTenantCode(), userInfo.getUserId());
            SysApiTokenConfig config = sysApiTokenConfigMapper.selectByNameAndSk(userInfo.getUserId(), sk);
            if (config == null) {
                // 分配token
                JSONObject authConfig = JSONObject.parseObject(tenant.getAuthConfig());
                JSONArray jsonArray = authConfig.getJSONArray("subsystem");
                List<String> subsys = jsonArray.toJavaList(String.class);
                ApiConfigPermission permission = authConfig.getObject("permission", ApiConfigPermission.class);
                SysApiTokenCreateReqBO createReqBO = new SysApiTokenCreateReqBO();
                createReqBO.setAppUserId(userInfo.getUserId());
                createReqBO.setAppTenantCode(sysUserSyncReqBO.getAppTenantCode());
                createReqBO.setAppName(userInfo.getUserId());
                createReqBO.setSubsystem(subsys);
                createReqBO.setPermission(permission);
                createReqBO.setExpireTime(3600 * 12);
                createReqBO.setSecretKey(sk);
                tokenRsp = sysTokenConfigApi.createTokenConfig(createReqBO);
            } else {
                log.info("同步外部用户信息-用户已存在token: {}", config);
                OuterUserAccessTokenReqBO reqBO = new OuterUserAccessTokenReqBO();
                reqBO.setAppTenantCode(sysUserSyncReqBO.getAppTenantCode());
                reqBO.setAppUserId(userInfo.getUserId());
                tokenRsp = accessToken(reqBO);
            }
            OuterUserSyncRspBO syncRspBO = new OuterUserSyncRspBO(userInfo, tokenRsp.getData());
            return BaseRspUtils.createSuccessRsp(syncRspBO, "success");
        }
        log.error("同步外部用户信息-注册用户失败: {}", sysUserSyncReqBO);
        return BaseRspUtils.createErrorRsp("同步失败");
    }

    private String geneSk(String tenant, String userId) {
        return "sk-" + NiccCommonUtil.stringToMD5(tenant + "_" + userId);
    }

    @MethodParamVerifyEnable
    @Override
    public Rsp<SysApiTokenRefreshRspBO> accessToken(OuterUserAccessTokenReqBO reqBO) {
        /**
         * 刷新token
         */
        SysApiTokenRefreshReqBO refreshReqBO = new SysApiTokenRefreshReqBO();
        refreshReqBO.setAppId(0);
        refreshReqBO.setSecretKey(geneSk(reqBO.getAppTenantCode(), reqBO.getAppUserId()));
        refreshReqBO.setAppName(reqBO.getAppUserId());
        return sysTokenConfigApi.refreshToken(refreshReqBO);
    }
}
