package com.tydic.nbchat.aitrain.core.web;

import com.tydic.nbchat.aitrain.api.TrainTestDialogBgApi;
import com.tydic.nbchat.aitrain.api.bo.trainTest.TrainTestDialogBgQueryBo;
import com.tydic.nbchat.aitrain.api.bo.trainTest.TrainTestDialogBgRspBo;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.base.bo.RspList;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 陪练任务-对话背景
 */
@Slf4j
@RestController
@RequestMapping("/aitrain/ai_test/dialog")
public class TrainTestDialogBgController {
    private final TrainTestDialogBgApi trainTestDialogBgApi;

    public TrainTestDialogBgController(TrainTestDialogBgApi trainTestDialogBgApi) {
        this.trainTestDialogBgApi = trainTestDialogBgApi;
    }

    /**
     * 获取系统背景与用户自定义背景
     */
    @PostMapping("/bg/list")
    public RspList<TrainTestDialogBgRspBo> getDialogBg(@Validated @RequestBody TrainTestDialogBgQueryBo request) {
        return trainTestDialogBgApi.getDialogBg(request);
    }
    /**
     * 用户自定义背景保存、更新
     */
    @PostMapping("/bg/save")
    public Rsp<TrainTestDialogBgRspBo> saveDialogBg(@RequestBody TrainTestDialogBgRspBo request) {
        return trainTestDialogBgApi.saveDialogBg(request);
    }
}
