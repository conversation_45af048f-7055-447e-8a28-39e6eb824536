<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tydic.nbchat.admin.mapper.RpTenantTdhInfoMapper">
    <resultMap id="BaseResultMap" type="com.tydic.nbchat.admin.mapper.po.RpTenantTdhInfo">
        <!--@mbg.generated-->
        <!--@Table rp_tenant_tdh_info-->
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="tenant_code" jdbcType="VARCHAR" property="tenantCode"/>
        <result column="day_data" jdbcType="DATE" property="dayData"/>
        <result column="tdh_type" jdbcType="CHAR" property="tdhType"/>
        <result column="count" jdbcType="SMALLINT" property="count"/>
        <result column="sale_price" jdbcType="SMALLINT" property="salePrice"/>
        <result column="total_price" jdbcType="SMALLINT" property="totalPrice"/>
        <result column="sale_score" jdbcType="SMALLINT" property="saleScore"/>
        <result column="total_score" jdbcType="SMALLINT" property="totalScore"/>
        <result column="usable" jdbcType="SMALLINT" property="usable"/>
        <result column="allRights" jdbcType="SMALLINT" property="allrights"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id, tenant_code, day_data, tdh_type, `count`, sale_price, total_price, sale_score,
        total_score, usable, allRights, update_time
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        <!--@mbg.generated-->
        select
        <include refid="Base_Column_List"/>
        from rp_tenant_tdh_info
        where id = #{id,jdbcType=INTEGER}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
        <!--@mbg.generated-->
        delete from rp_tenant_tdh_info
        where id = #{id,jdbcType=INTEGER}
    </delete>
    <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.tydic.nbchat.admin.mapper.po.RpTenantTdhInfo" useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into rp_tenant_tdh_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="tenantCode != null and tenantCode != ''">
                tenant_code,
            </if>
            <if test="dayData != null">
                day_data,
            </if>
            <if test="tdhType != null and tdhType != ''">
                tdh_type,
            </if>
            <if test="count != null">
                `count`,
            </if>
            <if test="salePrice != null">
                sale_price,
            </if>
            <if test="totalPrice != null">
                total_price,
            </if>
            <if test="saleScore != null">
                sale_score,
            </if>
            <if test="totalScore != null">
                total_score,
            </if>
            <if test="usable != null">
                usable,
            </if>
            <if test="allrights != null">
                allRights,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="tenantCode != null and tenantCode != ''">
                #{tenantCode,jdbcType=VARCHAR},
            </if>
            <if test="dayData != null">
                #{dayData,jdbcType=DATE},
            </if>
            <if test="tdhType != null and tdhType != ''">
                #{tdhType,jdbcType=CHAR},
            </if>
            <if test="count != null">
                #{count,jdbcType=SMALLINT},
            </if>
            <if test="salePrice != null">
                #{salePrice,jdbcType=SMALLINT},
            </if>
            <if test="totalPrice != null">
                #{totalPrice,jdbcType=SMALLINT},
            </if>
            <if test="saleScore != null">
                #{saleScore,jdbcType=SMALLINT},
            </if>
            <if test="totalScore != null">
                #{totalScore,jdbcType=SMALLINT},
            </if>
            <if test="usable != null">
                #{usable,jdbcType=SMALLINT},
            </if>
            <if test="allrights != null">
                #{allrights,jdbcType=SMALLINT},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.tydic.nbchat.admin.mapper.po.RpTenantTdhInfo">
        <!--@mbg.generated-->
        update rp_tenant_tdh_info
        <set>
            <if test="tenantCode != null and tenantCode != ''">
                tenant_code = #{tenantCode,jdbcType=VARCHAR},
            </if>
            <if test="dayData != null">
                day_data = #{dayData,jdbcType=DATE},
            </if>
            <if test="tdhType != null and tdhType != ''">
                tdh_type = #{tdhType,jdbcType=CHAR},
            </if>
            <if test="count != null">
                `count` = #{count,jdbcType=SMALLINT},
            </if>
            <if test="salePrice != null">
                sale_price = #{salePrice,jdbcType=SMALLINT},
            </if>
            <if test="totalPrice != null">
                total_price = #{totalPrice,jdbcType=SMALLINT},
            </if>
            <if test="saleScore != null">
                sale_score = #{saleScore,jdbcType=SMALLINT},
            </if>
            <if test="totalScore != null">
                total_score = #{totalScore,jdbcType=SMALLINT},
            </if>
            <if test="usable != null">
                usable = #{usable,jdbcType=SMALLINT},
            </if>
            <if test="allrights != null">
                allRights = #{allrights,jdbcType=SMALLINT},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=INTEGER}
    </update>
    <update id="updateBatchSelective" parameterType="java.util.List">
        <!--@mbg.generated-->
        update rp_tenant_tdh_info
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="tenant_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.tenantCode != null">
                        when id = #{item.id,jdbcType=INTEGER} then #{item.tenantCode,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="day_data = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.dayData != null">
                        when id = #{item.id,jdbcType=INTEGER} then #{item.dayData,jdbcType=DATE}
                    </if>
                </foreach>
            </trim>
            <trim prefix="tdh_type = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.tdhType != null">
                        when id = #{item.id,jdbcType=INTEGER} then #{item.tdhType,jdbcType=CHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="`count` = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.count != null">
                        when id = #{item.id,jdbcType=INTEGER} then #{item.count,jdbcType=SMALLINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="sale_price = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.salePrice != null">
                        when id = #{item.id,jdbcType=INTEGER} then #{item.salePrice,jdbcType=SMALLINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="total_price = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.totalPrice != null">
                        when id = #{item.id,jdbcType=INTEGER} then #{item.totalPrice,jdbcType=SMALLINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="sale_score = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.saleScore != null">
                        when id = #{item.id,jdbcType=INTEGER} then #{item.saleScore,jdbcType=SMALLINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="total_score = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.totalScore != null">
                        when id = #{item.id,jdbcType=INTEGER} then #{item.totalScore,jdbcType=SMALLINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="usable = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.usable != null">
                        when id = #{item.id,jdbcType=INTEGER} then #{item.usable,jdbcType=SMALLINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="allRights = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.allrights != null">
                        when id = #{item.id,jdbcType=INTEGER} then #{item.allrights,jdbcType=SMALLINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="update_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.updateTime != null">
                        when id = #{item.id,jdbcType=INTEGER} then #{item.updateTime,jdbcType=TIMESTAMP}
                    </if>
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.id,jdbcType=INTEGER}
        </foreach>
    </update>
    <insert id="insertStatisticsByRecord">
        INSERT INTO rp_tenant_tdh_info (tenant_code, day_data, tdh_type, count, total_price, total_score, update_time)
        SELECT tcr.tenant_code,
               tcr.day_data,
               tcr.customize_type                                         AS tdh_type,
               ifnull(sum(if(tcr.customize_status = '4', 1, 0)), 0)       AS count,
               ifnull(sum(if(ptr.pay_type = 'DOU', 0, ptr.pay_price)), 0) AS total_price,
               ifnull(sum(if(ptr.pay_type = 'DOU', ptr.pay_price, 0)), 0) AS total_score,
               current_timestamp                                          AS update_time
        FROM (SELECT tenant_code,
                     customize_type,
                     customize_status,
                     order_no,
                     date_format(create_time, '%Y-%m-%d') AS day_data
              FROM tdh_customize_record
              WHERE customize_type IN ('2d', 'audio', '2.5d_mtk')) tcr
                 LEFT JOIN (SELECT order_no, pay_type, pay_price
                            FROM pay_trade_record
                            WHERE pay_status IN ('SUCCESS', 'TRADE_SUCCESS')
                              AND refund_no = '') ptr
                           ON tcr.order_no = ptr.order_no
        WHERE day_data = current_date - INTERVAL 1 DAY
        GROUP BY tcr.tenant_code, tdh_type
    </insert>
</mapper>
