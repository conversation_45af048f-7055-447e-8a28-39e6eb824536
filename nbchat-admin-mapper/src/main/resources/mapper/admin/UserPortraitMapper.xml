<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tydic.nbchat.admin.mapper.UserPortraitMapper">
    <resultMap id="UserPortraitMap" type="com.tydic.nbchat.admin.mapper.po.UserPortraitBO">
        <result column="tenant_code" property="tenantCode"/>
        <result column="user_id" property="userId"/>
        <result column="user_name" property="userName"/>
        <result column="phone" property="phone"/>
        <result column="user_role" property="userRole"/>
        <result column="tenant_name" property="tenantName"/>
        <result column="reg_time" property="regTime"/>
        <result column="high_vip_status" property="highVipStatus"/>
        <result column="high_vip_months" property="highVipMonths"/>
        <result column="high_vip_days_remaining" property="highVipDaysRemaining"/>
        <result column="expert_vip_status" property="expertVipStatus"/>
        <result column="expert_vip_months" property="expertVipMonths"/>
        <result column="expert_vip_days_remaining" property="expertVipDaysRemaining"/>
        <result column="expert_max_vip_status" property="expertMaxVipStatus"/>
        <result column="expert_max_vip_months" property="expertMaxVipMonths"/>
        <result column="expert_max_vip_days_remaining" property="expertMaxVipDaysRemaining"/>
        <result column="score_balance" property="scoreBalance"/>
        <result column="total_pay_amount" property="totalPayAmount"/>
        <result column="score_recharge_total" property="scoreRechargeTotal"/>
        <result column="video_make_times" property="videoMakeTimes"/>
        <result column="video_avg_duration" property="videoAvgDuration"/>
        <result column="ppt_make_times" property="pptMakeTimes"/>
        <result column="image_actual_times" property="imageActualTimes"/>
        <result column="audio_actual_times" property="audioActualTimes"/>
        <result column="total_activity_days" property="totalActivityDays"/>
        <result column="last_30_activity_days" property="last30ActivityDays"/>
    </resultMap>
    <select id="selectUserPortraitInfo" resultMap="UserPortraitMap">
        WITH orud AS (SELECT tenant_code,
        user_id,
        user_name,
        phone,
        reg_time
        FROM op_rp_user_detail
        WHERE tenant_code = #{tenantCode,jdbcType=VARCHAR}
        AND user_id = #{userId,jdbcType=VARCHAR}),

        tdh AS (SELECT sum(if(customize_type = 'audio', NULL, 1)) AS image_actual_times,
        sum(if(customize_type = 'audio', 1, NULL)) AS audio_actual_times
        FROM tdh_customize_record
        WHERE customize_status = '4'
        AND order_status = '1'
        AND tenant_code = #{tenantCode,jdbcType=VARCHAR}
        AND user_id = #{userId,jdbcType=VARCHAR}),

        vip_info AS (SELECT vip_type,
        vip_status,
        timestampdiff(MONTH, vip_start, vip_end) AS vip_month_count,
        if(vip_status = '1', datediff(vip_end, current_date) + 1, datediff(vip_end, current_date)) AS vip_days_remaining
        FROM nbchat_user_vip
        WHERE vip_type IN ('2', '3', '4')
        AND tenant_code = #{tenantCode,jdbcType=VARCHAR}
        AND user_id = #{userId,jdbcType=VARCHAR}),

        vip AS (SELECT min(if(vip_type = '2', vip_status, '2')) AS high_vip_status,
        max(if(vip_type = '2', vip_month_count, NULL)) AS high_vip_month_count,
        max(if(vip_type = '2', vip_days_remaining, NULL)) AS high_vip_days_remaining,
        min(if(vip_type = '3', vip_status, '2')) AS expert_vip_status,
        max(if(vip_type = '3', vip_month_count, NULL)) AS expert_vip_month_count,
        max(if(vip_type = '3', vip_days_remaining, NULL)) AS expert_vip_days_remaining,
        min(if(vip_type = '4', vip_status, '2')) AS expert_max_vip_status,
        max(if(vip_type = '4', vip_month_count, NULL)) AS expert_max_vip_month_count,
        max(if(vip_type = '4', vip_days_remaining, NULL)) AS expert_max_vip_days_remaining
        FROM vip_info),

        balance AS (SELECT sum(score) AS score_balance
        FROM nbchat_user_balance
        WHERE tenant_code = #{tenantCode,jdbcType=VARCHAR}
        AND user_id = #{userId,jdbcType=VARCHAR}),

        pay AS (SELECT sum(pay_price) AS total_pay_amount
        FROM pay_trade_record
        WHERE refund_no = ''
        AND pay_status IN ('SUCCESS', 'TRADE_SUCCESS')
        AND tenant_code = #{tenantCode,jdbcType=VARCHAR}
        AND user_id = #{userId,jdbcType=VARCHAR}),

        score AS (SELECT sum(score) AS score_recharge_total
        FROM nbchat_user_bill_record
        WHERE type = '1'
        AND tenant_code = #{tenantCode,jdbcType=VARCHAR}
        AND user_id = #{userId,jdbcType=VARCHAR}),

        video AS (SELECT count(0) AS video_make_times,
        sum(if(task_state = '1', 1, NULL)) AS video_success_times,
        sum(if(task_state = '1', video_duration, 0)) / 60 AS video_total_duration
        FROM tdh_creation_task
        WHERE tenant_code = #{tenantCode,jdbcType=VARCHAR}
        AND user_id = #{userId,jdbcType=VARCHAR}),

        ppt AS (SELECT count(0) AS ppt_make_times
        FROM ppt_creation_record
        WHERE tenant_code = #{tenantCode,jdbcType=VARCHAR}
        AND user_id = #{userId,jdbcType=VARCHAR}),

        activity_info AS (SELECT date_format(create_time, '%Y-%m-%d') AS day_data
        FROM sys_user_operate_log
        WHERE tenant_code = #{tenantCode,jdbcType=VARCHAR}
        AND user_id = #{userId,jdbcType=VARCHAR}
        GROUP BY day_data),

        suol AS (SELECT count(0) AS total_activity_days,
        sum(if(datediff(current_date, day_data) > 30, 0, 1)) AS last_30_activity_days
        FROM activity_info)

        SELECT orud.tenant_code,
        orud.user_id,
        orud.user_name,
        orud.phone,
        NULL AS user_role,
        (SELECT tenant_name FROM nbchat_sys_tenant WHERE tenant_code = #{tenantCode,jdbcType=VARCHAR}) AS tenant_name,
        orud.reg_time,
        ifnull(vip.high_vip_status, '2') AS high_vip_status,
        ifnull(vip.high_vip_month_count, 0) AS high_vip_months,
        vip.high_vip_days_remaining,
        ifnull(vip.expert_vip_status, '2') AS expert_vip_status,
        ifnull(vip.expert_vip_month_count, 0) AS expert_vip_months,
        vip.expert_vip_days_remaining,
        ifnull(vip.expert_max_vip_status, '2') AS expert_max_vip_status,
        ifnull(vip.expert_max_vip_month_count, 0) AS expert_max_vip_months,
        vip.expert_max_vip_days_remaining,
        ifnull(balance.score_balance, 0) AS score_balance,
        ifnull(pay.total_pay_amount, 0) AS total_pay_amount,
        ifnull(score.score_recharge_total, 0) AS score_recharge_total,
        ifnull(video.video_make_times, 0) AS video_make_times,
        if(video.video_success_times IS NOT NULL, ifnull(round(video.video_total_duration / video.video_success_times, 1), 0.0), NULL) AS video_avg_duration,
        ifnull(ppt.ppt_make_times, 0) AS ppt_make_times,
        ifnull(tdh.image_actual_times, 0) AS image_actual_times,
        ifnull(tdh.audio_actual_times, 0) AS audio_actual_times,
        ifnull(suol.total_activity_days, 0) AS total_activity_days,
        ifnull(suol.last_30_activity_days, 0) AS last_30_activity_days
        FROM orud,
        balance,
        pay,
        score,
        video,
        ppt,
        tdh,
        vip,
        suol
    </select>
    <resultMap id="UserPortraitTimelineMap" type="com.tydic.nbchat.admin.mapper.po.UserPortraitTimelineBO">
        <result column="action_time" property="actionTime"/>
        <result column="action_name" property="actionName"/>
        <result column="ext_params" property="extParams"/>
    </resultMap>
    <select id="selectUserPortraitTimelineInfo" resultMap="UserPortraitTimelineMap">
        SELECT *
        FROM (SELECT reg_time AS action_time,
        '用户注册' AS action_name,
        if((prom_channel IS NOT NULL AND prom_channel != '') OR (prom_key IS NOT NULL AND prom_key != ''), json_object('prom_channel', prom_channel, 'prom_key', prom_key),
        '{}') AS ext_params
        FROM op_rp_user_detail
        WHERE tenant_code = #{tenantCode,jdbcType=VARCHAR}
        AND user_id = #{userId,jdbcType=VARCHAR}
        UNION ALL
        SELECT end_time AS action_time,
        '会员过期' AS action_name,
        CASE
        WHEN vip_type = '2' AND valid_days >= 365 THEN json_object('goods_name', '高级会员-年')
        WHEN vip_type = '2' AND valid_days >= 31 THEN json_object('goods_name', '高级会员-月')
        WHEN vip_type = '3' AND valid_days >= 365 THEN json_object('goods_name', '专业会员-年')
        WHEN vip_type = '3' AND valid_days >= 31 THEN json_object('goods_name', '专业会员-月')
        WHEN vip_type = '4' AND valid_days >= 365 THEN json_object('goods_name', '专业会员(MAX版)-年')
        WHEN vip_type = '4' AND valid_days >= 31 THEN json_object('goods_name', '专业会员(MAX版)-月')
        END AS ext_params
        FROM nbchat_user_vip_log
        WHERE is_refund = '0'
        AND vip_type IN ('2', '3', '4')
        AND tenant_code = #{tenantCode,jdbcType=VARCHAR}
        AND user_id = #{userId,jdbcType=VARCHAR}
        UNION ALL
        SELECT pay_time AS action_time,
        '支付购买' AS action_name,
        json_object('goods_name', goods_name, 'pay_type', pay_type, 'pay_price', pay_price) AS ext_params
        FROM pay_trade_record ptr
        LEFT JOIN (SELECT order_no,
        sku_name AS goods_name
        FROM pay_order_item
        UNION ALL
        SELECT order_no,
        sku_name AS goods_name
        FROM tdh_customize_record tcr
        LEFT JOIN pay_goods_sku pgs
        ON pgs.sku_id = tcr.sku_id) t_order
        ON ptr.order_no = t_order.order_no
        WHERE pay_status IN ('SUCCESS', 'TRADE_SUCCESS', 'REFUND_SUCCESS')
        AND pay_type != 'DOU'
        AND ptr.order_no IS NOT NULL
        AND tenant_code = #{tenantCode,jdbcType=VARCHAR}
        AND user_id = #{userId,jdbcType=VARCHAR}
        UNION ALL
        SELECT pay_time AS action_time,
        '支付退款' AS action_name,
        json_object('goods_name', sku_name, 'pay_type', pay_type, 'pay_price', -pay_price) AS ext_params
        FROM pay_trade_record ptr
        LEFT JOIN (SELECT order_no,
        sku_name
        FROM pay_order_item
        UNION ALL
        SELECT order_no,
        sku_name
        FROM tdh_customize_record tcr
        LEFT JOIN pay_goods_sku pgs
        ON pgs.sku_id = tcr.sku_id) t_order
        ON ptr.refund_no = t_order.order_no
        WHERE pay_status IN ('SUCCESS', 'TRADE_SUCCESS', 'REFUND_SUCCESS')
        AND pay_type != 'DOU'
        AND ptr.order_no IS NULL
        AND tenant_code = #{tenantCode,jdbcType=VARCHAR}
        AND user_id = #{userId,jdbcType=VARCHAR}
        UNION ALL
        SELECT action_time AS action_time,
        '用户制作' AS action_name,
        json_object('ppt_make_count', sum(ifnull(ppt_make_count, 0)), 'video_make_count', sum(ifnull(video_make_count, 0)), 'video_total_duration', round(sum(ifnull(video_total_duration, 0)) / 60, 1))
        AS ext_params
        FROM (SELECT date_format(create_time, '%Y-%m-%d 23:59:59') AS action_time,
        count(0) AS ppt_make_count,
        NULL AS video_make_count,
        NULL AS video_total_duration
        FROM ppt_creation_record
        WHERE tenant_code = #{tenantCode,jdbcType=VARCHAR}
        AND user_id = #{userId,jdbcType=VARCHAR}
        GROUP BY action_time
        UNION ALL
        SELECT date_format(create_time, '%Y-%m-%d 23:59:59') AS action_time,
        NULL AS ppt_make_count,
        count(0) AS video_make_count,
        sum(if(task_state = '1', video_duration, 0)) AS video_total_duration
        FROM tdh_creation_task
        WHERE creation_id != '1'
        AND tenant_code = #{tenantCode,jdbcType=VARCHAR}
        AND user_id = #{userId,jdbcType=VARCHAR}
        GROUP BY action_time) make
        GROUP BY action_time
        UNION ALL
        SELECT max(create_time) AS action_time,
        '最后访问' AS action_name,
        '{}' AS ext_params
        FROM sys_user_operate_log
        WHERE tenant_code = #{tenantCode,jdbcType=VARCHAR}
        AND user_id = #{userId,jdbcType=VARCHAR}) timeline
        WHERE action_time IS NOT NULL
        ORDER BY action_time
    </select>
</mapper>
