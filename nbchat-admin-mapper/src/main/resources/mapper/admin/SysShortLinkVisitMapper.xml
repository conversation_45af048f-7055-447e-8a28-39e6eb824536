<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.tydic.nbchat.admin.mapper.SysShortLinkVisitMapper">
    <!-- 基础字段映射 -->
    <resultMap id="BaseResultMap" type="com.tydic.nbchat.admin.mapper.po.SysShortLinkVisit">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="short_code" property="shortCode" jdbcType="VARCHAR"/>
        <result column="user_id" property="userId" jdbcType="VARCHAR"/>
        <result column="visit_time" property="visitTime" jdbcType="TIMESTAMP"/>
        <result column="client_ip" property="clientIp" jdbcType="VARCHAR"/>
        <result column="user_agent" property="userAgent" jdbcType="VARCHAR"/>
        <result column="ext_json" property="extJson" jdbcType="VARCHAR"/>
    </resultMap>

    <!-- 查询语句 -->
    <select id="selectById" resultMap="BaseResultMap">
        SELECT * FROM sys_short_link_visit
        WHERE id = #{id,jdbcType=BIGINT}
    </select>

    <select id="selectByShortCode" resultMap="BaseResultMap">
        SELECT * FROM sys_short_link_visit
        WHERE short_code = #{shortCode,jdbcType=VARCHAR}
        ORDER BY visit_time DESC
    </select>

    <select id="selectByUserId" resultMap="BaseResultMap">
        SELECT * FROM sys_short_link_visit
        WHERE user_id = #{userId,jdbcType=VARCHAR}
        ORDER BY visit_time DESC
    </select>

    <!-- 插入语句 -->
    <insert id="insert" parameterType="com.tydic.nbchat.admin.mapper.po.SysShortLinkVisit" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO sys_short_link_visit
            (short_code, user_id, visit_time, client_ip, user_agent, ext_json)
        VALUES
            (#{shortCode,jdbcType=VARCHAR}, #{userId,jdbcType=VARCHAR},
             #{visitTime,jdbcType=TIMESTAMP}, #{clientIp,jdbcType=VARCHAR},
             #{userAgent,jdbcType=VARCHAR}, #{extJson,jdbcType=VARCHAR})
    </insert>

    <!-- 批量插入 -->
    <insert id="insertBatch">
        INSERT INTO sys_short_link_visit
        (short_code, user_id, visit_time, client_ip, user_agent, ext_json)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.shortCode,jdbcType=VARCHAR}, #{item.userId,jdbcType=VARCHAR},
            #{item.visitTime,jdbcType=TIMESTAMP}, #{item.clientIp,jdbcType=VARCHAR},
            #{item.userAgent,jdbcType=VARCHAR}, #{item.extJson,jdbcType=VARCHAR})
        </foreach>
    </insert>

    <!-- 访问统计 -->
    <select id="selectVisitStats" resultType="map">
        SELECT
            short_code,
            COUNT(*) AS total_visits,
            COUNT(DISTINCT user_id) AS unique_users,
            MIN(visit_time) AS first_visit,
            MAX(visit_time) AS last_visit
        FROM sys_short_link_visit
        WHERE short_code = #{shortCode,jdbcType=VARCHAR}
        GROUP BY short_code
    </select>

    <!-- 访问趋势 -->
    <select id="selectVisitTrend" resultType="map">
        SELECT
            DATE_FORMAT(visit_time, '%Y-%m-%d') AS visit_date,
            COUNT(*) AS visit_count,
            COUNT(DISTINCT user_id) AS unique_users
        FROM sys_short_link_visit
        WHERE short_code = #{shortCode,jdbcType=VARCHAR}
          AND visit_time >= DATE_SUB(NOW(), INTERVAL #{days} DAY)
        GROUP BY DATE_FORMAT(visit_time, '%Y-%m-%d')
        ORDER BY visit_date
    </select>
</mapper>