<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.tydic.nbchat.admin.mapper.SysShortLinkMapper">
    <!-- 基础字段映射 -->
    <resultMap id="BaseResultMap" type="com.tydic.nbchat.admin.mapper.po.SysShortLink">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="tenant_code" property="tenantCode" jdbcType="VARCHAR"/>
        <result column="short_code" property="shortCode" jdbcType="VARCHAR"/>
        <result column="origin_url" property="originUrl" jdbcType="VARCHAR"/>
        <result column="biz_type" property="bizType" jdbcType="VARCHAR"/>
        <result column="biz_id" property="bizId" jdbcType="VARCHAR"/>
        <result column="user_id" property="userId" jdbcType="VARCHAR"/>
        <result column="first_time" property="firstTime" jdbcType="TIMESTAMP"/>
        <result column="last_time" property="lastTime" jdbcType="TIMESTAMP"/>
        <result column="expire_time" property="expireTime" jdbcType="TIMESTAMP"/>
        <result column="ext_json" property="extJson" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <!-- 查询语句 -->
    <select id="selectByShortCode" resultMap="BaseResultMap">
        SELECT * FROM sys_short_link
        WHERE short_code = #{shortCode,jdbcType=VARCHAR}
          AND expire_time > now()
        limit 1
    </select>

    <select id="selectByBizTypeAndId" resultMap="BaseResultMap">
        SELECT * FROM sys_short_link
        WHERE biz_type = #{bizType,jdbcType=VARCHAR}
          AND biz_id = #{bizId,jdbcType=VARCHAR}
          AND expire_time > now()
        ORDER BY create_time DESC
        limit 1
    </select>

    <select id="selectByUserId" resultMap="BaseResultMap">
        SELECT * FROM sys_short_link
        WHERE user_id = #{userId,jdbcType=VARCHAR}
        ORDER BY create_time DESC
    </select>

    <!-- 插入语句 -->
    <insert id="insert" parameterType="com.tydic.nbchat.admin.mapper.po.SysShortLink" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO sys_short_link
        (tenant_code, short_code, origin_url, biz_type, biz_id, user_id,
         first_time, last_time, expire_time, ext_json)
        VALUES
            (#{tenantCode,jdbcType=VARCHAR}, #{shortCode,jdbcType=VARCHAR},
             #{originUrl,jdbcType=VARCHAR}, #{bizType,jdbcType=VARCHAR},
             #{bizId,jdbcType=VARCHAR}, #{userId,jdbcType=VARCHAR},
             #{firstTime,jdbcType=TIMESTAMP}, #{lastTime,jdbcType=TIMESTAMP},
             #{expireTime,jdbcType=TIMESTAMP}, #{extJson,jdbcType=VARCHAR})
    </insert>

    <!-- 选择性更新 -->
    <update id="updateByShortCodeSelective" parameterType="com.tydic.nbchat.admin.mapper.po.SysShortLink">
        UPDATE sys_short_link
        <set>
            <if test="tenantCode != null">tenant_code = #{tenantCode,jdbcType=VARCHAR},</if>
            <if test="originUrl != null">origin_url = #{originUrl,jdbcType=VARCHAR},</if>
            <if test="bizType != null">biz_type = #{bizType,jdbcType=VARCHAR},</if>
            <if test="bizId != null">biz_id = #{bizId,jdbcType=VARCHAR},</if>
            <if test="userId != null">user_id = #{userId,jdbcType=VARCHAR},</if>
            <if test="firstTime != null">first_time = #{firstTime,jdbcType=TIMESTAMP},</if>
            <if test="lastTime != null">last_time = #{lastTime,jdbcType=TIMESTAMP},</if>
            <if test="expireTime != null">expire_time = #{expireTime,jdbcType=TIMESTAMP},</if>
            <if test="extJson != null">ext_json = #{extJson,jdbcType=VARCHAR}</if>
        </set>
        WHERE short_code = #{shortCode,jdbcType=VARCHAR}
    </update>

    <!-- 更新访问时间 -->
    <update id="updateVisitTime">
        UPDATE sys_short_link
        <set>
            first_time = CASE
            WHEN first_time IS NULL THEN #{visitTime,jdbcType=TIMESTAMP}
            ELSE first_time
            END,
            last_time = #{visitTime,jdbcType=TIMESTAMP},
            update_time =#{visitTime,jdbcType=TIMESTAMP}
        </set>
        WHERE short_code = #{shortCode,jdbcType=VARCHAR}
    </update>

    <!-- 删除语句 -->
    <delete id="deleteByShortCode">
        DELETE FROM sys_short_link
        WHERE short_code = #{shortCode,jdbcType=VARCHAR}
    </delete>
</mapper>