<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tydic.nbchat.admin.mapper.RpTenantDetailInfoMapper">
    <insert id="insertStatisticsByUser">
        INSERT INTO rp_tenant_detail_info (tenant_code, day_data, user_count, update_time)
        SELECT tenant_code,
               current_date - INTERVAL 1 DAY AS day_data,
               count(0)                      AS user_count,
               current_timestamp             AS update_time
        FROM nbchat_sys_user_tenant
        WHERE user_status = '1'
          AND date_format(create_time, '%Y-%m-%d') = current_date - INTERVAL 1 DAY
        GROUP BY tenant_code
    </insert>
    <insert id="insertStatisticsByExam">
        INSERT INTO rp_tenant_detail_info (tenant_code, day_data, exam_make_times, update_time)
        SELECT tenant_code,
               date_format(create_time, '%Y-%m-%d')                                   AS day_data,
               sum(fill_num + judge_num + choice_m_num + choice_s_num + question_num) AS exam_make_times,
               current_timestamp                                                      AS update_time
        FROM exam_creation_record
        WHERE date_format(create_time, '%Y-%m-%d') = current_date - INTERVAL 1 DAY
        GROUP BY tenant_code
    </insert>
    <insert id="insertStatisticsByPay">
        INSERT INTO rp_tenant_detail_info (tenant_code, day_data, total_pay_amount, total_pay_times, update_time)
        SELECT tenant_code,
               current_date - INTERVAL 1 DAY AS day_data,
               sum(pay_price)                AS total_pay_amount,
               count(0)                      AS total_pay_times,
               current_timestamp             AS update_time
        FROM pay_trade_record
        WHERE refund_no = ''
          AND pay_type != 'DOU'
          AND pay_status IN ('SUCCESS', 'TRADE_SUCCESS')
          AND date_format(pay_time, '%Y-%m-%d') = current_date - INTERVAL 1 DAY
        GROUP BY tenant_code
    </insert>
    <insert id="insertStatisticsByBill">
        INSERT INTO rp_tenant_detail_info (tenant_code, day_data, score_recharge_total, score_consume_total, score_lose_total, update_time)
        SELECT tenant_code,
               current_date - INTERVAL 1 DAY                                                 AS day_data,
               sum(if(type = '1', score, 0))                                                 AS score_recharge_total,
               abs(sum(if(type IN ('0', '2', '3') AND biz_code != 'dou_expired', score, 0))) AS score_consume_total,
               abs(sum(if(type IN ('0', '2', '3') AND biz_code = 'dou_expired', score, 0)))  AS score_lose_total,
               current_timestamp                                                             AS update_time
        FROM nbchat_user_bill_record
        WHERE date_format(trade_time, '%Y-%m-%d') = current_date - INTERVAL 1 DAY
        GROUP BY tenant_code
    </insert>
    <insert id="insertStatisticsByBalance">
        INSERT INTO rp_tenant_detail_info (tenant_code, day_data, score_total_balance, score_personal_balance, score_tenant_balance, update_time)
        SELECT tenant_code,
               current_date - INTERVAL 1 DAY            AS day_data,
               sum(score)                               AS score_total_balance,
               sum(if(tenant_code = user_id, 0, score)) AS score_personal_balance,
               sum(if(tenant_code = user_id, score, 0)) AS score_tenant_balance,
               current_timestamp                        AS update_time
        FROM nbchat_user_balance
        GROUP BY tenant_code
    </insert>
    <insert id="insertStatisticsByOperate">
        INSERT INTO rp_tenant_detail_info (tenant_code, day_data, access_times, first_access_time, last_access_time, ppt_download_times, video_download_times, exam_chick_times, exam_download_times,
                                           update_time)
        SELECT tenant_code,
               current_date - INTERVAL 1 DAY       AS day_data,
               1                                   AS access_times,
               min(create_time)                    AS first_access_time,
               max(create_time)                    AS last_access_time,
               sum(if(type = 'ppt_down', 1, 0))    AS ppt_download_times,
               sum(if(type = 'video_down', 1, 0))  AS video_download_times,
               sum(if(type = 'exam_gene', 1, 0))   AS exam_chick_times,
               sum(if(type = 'exam_export', 1, 0)) AS exam_download_times,
               current_timestamp                   AS update_time
        FROM sys_user_operate_log
        WHERE tenant_code IS NOT NULL
          AND date_format(create_time, '%Y-%m-%d') = current_date - INTERVAL 1 DAY
        GROUP BY tenant_code
    </insert>
    <insert id="insertStatisticsByPpt">
        INSERT INTO rp_tenant_detail_info (tenant_code, day_data, ppt_make_times, ppt_ai_count, ppt_doc_count, ppt_txt_count, update_time)
        SELECT tenant_code,
               current_date - INTERVAL 1 DAY    AS day_data,
               count(0)                         AS ppt_make_times,
               sum(if(creation_type = 1, 1, 0)) AS ppt_ai_count,
               sum(if(creation_type = 2, 1, 0)) AS ppt_doc_count,
               sum(if(creation_type = 3, 1, 0)) AS ppt_txt_count,
               current_timestamp                AS update_time
        FROM ppt_creation_record
        WHERE date_format(create_time, '%Y-%m-%d') = current_date - INTERVAL 1 DAY
        GROUP BY tenant_code
    </insert>
    <insert id="insertStatisticsByTdhTask">
        INSERT INTO rp_tenant_detail_info (tenant_code, day_data, video_build_times, video_total_duration, video_avg_duration, video_total_size, video_cartoon_count, video_25d_count, video_2d_count,
                                           video_none_count, video_custom_25d_count, video_custom_2d_count, video_custom_cartoon_count, video_audio_count, update_time)
        SELECT tct.tenant_code,
               current_date - INTERVAL 1 DAY                                                                                                              AS day_data,
               count(tct.creation_id)                                                                                                                     AS video_build_times,
               sum(if(tct.task_state = '1', tct.video_duration, 0))                                                                                       AS video_total_duration,
               if(sum(if(tct.task_state = '1', 1, 0)) = 0, 0, sum(if(tct.task_state = '1', tct.video_duration, 0)) / sum(if(tct.task_state = '1', 1, 0))) AS video_avg_duration,
               sum(if(tct.task_state = '1', tct.file_size / 1048576, 0))                                                                                  AS video_total_size,
               sum(if(tct.tdh_type = '2d_gif', 1, 0))                                                                                                     AS video_cartoon_count,
               sum(if(tct.tdh_type IN ('2.5d', '2.5d_wp'), 1, 0))                                                                                         AS video_25d_count,
               sum(if(tct.tdh_type IN ('2d', '2d_echo'), 1, 0))                                                                                           AS video_2d_count,
               sum(if(tct.tdh_type = 'none', 1, 0))                                                                                                       AS video_none_count,
               sum(if(tct.tdh_type = '2.5d_mtk', 1, 0))                                                                                                   AS video_custom_25d_count,
               sum(if(tct.tdh_type = '2d_mtk', 1, 0))                                                                                                     AS video_custom_2d_count,
               0                                                                                                                                          AS video_custom_cartoon_count, -- TODO 定制卡通还没有
               sum(if(tct.voice_name = '', 0, if((SELECT count(0) FROM tdh_customize_record WHERE volc_id = tct.voice_name) = 0, 0, 1)))                  AS video_audio_count,
               current_timestamp                                                                                                                          AS update_time
        FROM tdh_creation_task tct
        WHERE tct.user_id != '1'
          AND date_format(tct.start_time, '%Y-%m-%d') = current_date - INTERVAL 1 DAY
        GROUP BY tct.tenant_code
    </insert>
    <insert id="insertStatisticsByTdhRecord">
        INSERT INTO rp_tenant_detail_info (tenant_code, day_data, video_make_times, video_ppt_count, video_ppt_upload_count, video_ppt_platform_count, video_word_speech_count, video_ai_count,
                                           video_word_generate_count, video_template_count, video_custom_count, update_time)
        SELECT tenant_code,
               current_date - INTERVAL 1 DAY                                AS day_data,
               count(0)                                                     AS video_make_times,
               sum(if(creation_source = '2', 1, 0))                         AS video_ppt_count,
               sum(if(creation_source = '2' AND creation_type = '1', 1, 0)) AS video_ppt_upload_count,
               sum(if(creation_source = '2' AND creation_type = '2', 1, 0)) AS video_ppt_platform_count,
               sum(if(creation_source = '3', 1, 0))                         AS video_word_speech_count,
               sum(if(creation_source = '4', 1, 0))                         AS video_ai_count,
               sum(if(creation_source = '5', 1, 0))                         AS video_word_generate_count,
               sum(if(creation_source = '6', 1, 0))                         AS video_template_count,
               sum(if(creation_source = '1', 1, 0))                         AS video_custom_count,
               current_timestamp                                            AS update_time
        FROM tdh_creation_record
        WHERE date_format(create_time, '%Y-%m-%d') = current_date - INTERVAL 1 DAY
        GROUP BY tenant_code
    </insert>
</mapper>
