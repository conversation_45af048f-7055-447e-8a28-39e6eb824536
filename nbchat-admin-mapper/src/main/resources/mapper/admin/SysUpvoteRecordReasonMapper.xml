<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tydic.nbchat.admin.mapper.SysUpvoteRecordReasonMapper">
  <resultMap id="BaseResultMap" type="com.tydic.nbchat.admin.mapper.po.SysUpvoteRecordReason">
    <id property="reasonId" column="reason_id" jdbcType="BIGINT" />
    <result property="upvoteId" column="upvote_id" jdbcType="BIGINT" />
    <result property="reason" column="reason" jdbcType="VARCHAR" />
    <result property="createTime" column="create_time" jdbcType="TIMESTAMP" />
  </resultMap>
  <sql id="Base_Column_List">
    reason_id, upvote_id, reason, create_time
  </sql>

  <select id="selectCountByReason" parameterType="com.tydic.nbchat.admin.mapper.po.SysUpvoteRecordReason"
          resultType="com.tydic.nbchat.admin.mapper.po.SysUpvoteRecordReportPO">
    SELECT d.reason,
    d.reasonCode,
    d.orderIndex,
    IFNULL(record.count, 0) AS recordCount
    FROM (SELECT dict_name AS reason, dict_value AS reasonCode, order_index AS orderIndex
    from sys_dict_config
    where dict_code = #{dictCode} order by order_index ) AS d
    LEFT JOIN (select r.reason, count(1) AS count
    from sys_upvote_record u,
    sys_upvote_record_reason r
    where u.id = r.upvote_id
    <if test="productModule != null and productModule != ''">
     and u.product_module = #{productModule}
    </if>
    group by r.reason) record
    ON record.reason = d.reason
    GROUP BY d.reason, d.reasonCode
    ORDER BY d.orderIndex
  </select>


  <!-- 批量插入数据 -->
  <insert id="insertBatch" parameterType="java.util.List">
    insert into sys_upvote_record_reason(
    upvote_id
    ,reason
    ,create_time
    ) values
    <foreach collection="list" item="item" index="index" separator=",">
      (
      #{item.upvoteId}
      ,#{item.reason}
      ,#{item.createTime}
      )
    </foreach>
  </insert>
  <delete id="deleteByUpvoteId" parameterType="java.lang.Long">
    delete from sys_upvote_record_reason
    where  upvote_id = #{upvoteId}
  </delete>
</mapper>