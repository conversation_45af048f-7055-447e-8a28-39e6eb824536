package com.tydic.nbchat.admin.mapper.po;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @create 2025/6/9
 * @since 1.0.0
 **/
@Data
public class SysShortLink implements Serializable {

    private static final long serialVersionUID = -7163388061472709589L;
    private Long id;                // 主键ID
    private String tenantCode;      // 租户编码
    private String shortCode;       // 短链唯一标识
    private String originUrl;       // 原始长链接
    private String bizType;         // 业务类型
    private String bizId;           // 业务主键ID
    private String userId;          // 创建人ID
    private Date firstTime;         // 首次访问时间
    private Date lastTime;          // 最后访问时间
    private Date expireTime;        // 过期时间
    private String extJson;       // 扩展信息(使用Jackson的JsonNode)
    private Date createTime;        // 创建时间
    private Date updateTime;        // 更新时间

}
