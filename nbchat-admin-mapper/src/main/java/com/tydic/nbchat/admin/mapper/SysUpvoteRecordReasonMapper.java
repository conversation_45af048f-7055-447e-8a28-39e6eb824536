package com.tydic.nbchat.admin.mapper;

import com.tydic.nbchat.admin.mapper.po.SysUpvoteRecordReason;
import com.tydic.nbchat.admin.mapper.po.SysUpvoteRecordReasonPO;
import com.tydic.nbchat.admin.mapper.po.SysUpvoteRecordReportPO;

import java.util.List;

public interface SysUpvoteRecordReasonMapper {
    /**
     * 数据报表分析（后台管理右侧）
     *
     * @param record 参数列表
     */
    List<SysUpvoteRecordReportPO> selectCountByReason(SysUpvoteRecordReason record);

    /**
     * 批量插入数据
     *
     * @param list 参数列表
     */
    void insertBatch(List<SysUpvoteRecordReasonPO> list);
    /**
     * 批量删除数据
     *
     * @param upvoteId 参数列表
     */
    int deleteByUpvoteId(Long upvoteId);
}