package com.tydic.nbchat.admin.mapper;

import com.tydic.nbchat.admin.mapper.po.RpTenantTdhInfo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/5/28 21:04
 * @description
 */

public interface RpTenantTdhInfoMapper {
    /**
     * delete by primary key
     *
     * @param id primaryKey
     * @return deleteCount
     */
    int deleteByPrimaryKey(Integer id);

    /**
     * insert record to table selective
     *
     * @param record the record
     * @return insert count
     */
    int insertSelective(RpTenantTdhInfo record);

    /**
     * select by primary key
     *
     * @param id primary key
     * @return object by primary key
     */
    RpTenantTdhInfo selectByPrimaryKey(Integer id);

    /**
     * update record selective
     *
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKeySelective(RpTenantTdhInfo record);

    int updateBatchSelective(@Param("list") List<RpTenantTdhInfo> list);

    int insertStatisticsByRecord();
}
