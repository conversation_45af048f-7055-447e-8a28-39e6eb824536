package com.tydic.nbchat.admin.mapper.po;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 评价记录原因表
 * sys_upvote_record_reason
 */
@Data
public class SysUpvoteRecordReason implements Serializable {
    /**
     * 主键
     */
    private Long reasonId;

    /**
     * 记录id
     */
    private Long upvoteId;

    /**
     * 原因 选择
     */
    private String reason;

    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 租户id
     */
    private String tenantCode;
    /**
     * 产品模块1：视频制作 2：ppt制作 3：形象/声音定制 4：智能出题 5：其他
     */
    private String productModule;

    private static final long serialVersionUID = 1L;
    /**
     * 字段编码
     */
    private String dictCode;
}