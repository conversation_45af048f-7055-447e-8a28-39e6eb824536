package com.tydic.nbchat.admin.mapper.po;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 用户评价-列表查询
 */
@Data
public class SysUpvoteRecordCondition implements Serializable {
    private static final long serialVersionUID = 400359936720169008L;
    /**
     * 用户id
     */
    private String userId;
    /**
     * 租户id
     */
    private String tenantCode;

    private String targetTenantCode;

    /**
     * 反馈时间 --对应sys_upvote_record表字段createTime
     */
    private Date feedbackTimeStart;
    private Date feedbackTimeEnd;

    /**
     * 视频时长 s
     */
    private Integer videoDuration;
    private Integer minVideoDuration;
    private Integer maxVideoDuration;

    /**
     * 创建时间 tdh_creation_record 表 字段createTime
     */
    private Date createTime;
    private Date createTimeStart;
    private Date createTimeEnd;
    /**
     * 处理时间
     */
    private Date handleTime;
    private Date handleTimeStart;
    private Date handleTimeEnd;
    /**
     * 制作时间
     */
    private Date startTime;
    /**
     * 制作时间
     */
    private Date endTime;

    /**
     * 状态: 0 待处理、1 已采纳、2 已失效、3、已审阅
     */
    private String upvoteState;
    /**
     * 用户属性
     */
    private String userType;

    /**
     * 手机号
     */
    private String phone;
    /**
     * 企业名称
     */
    private String companyName;

    /**
     * 评分级别：1-5颗星
     */
    private Integer rating;
    /**
     * 产品模块1：视频制作 2：ppt制作 3：形象/声音定制 4：智能出题 5：其他
     */
    private String productModule;
    /**
     * 排序
     */
    private String orderBy;

    /**
     * 创作方式 1-AI生成PPT 2-上传文档转PPT 3-资料PPT
     */
    private String creationType;
    /**
     * 0:普通ppt 1:美化ppt
     */
    private String pptType;
    /**
     * PPT页数
     */
    private Integer minPartCount;
    /**
     * PPT页数
     */
    private Integer maxPartCount;
    /**
     * 支付状态 1 已支付，0 未支付
     */
    private String paymentStatus;

}

