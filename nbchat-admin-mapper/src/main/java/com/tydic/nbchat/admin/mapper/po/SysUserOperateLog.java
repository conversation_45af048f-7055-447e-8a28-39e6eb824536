package com.tydic.nbchat.admin.mapper.po;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025/5/19 10:42
 * @description 用户操作日志表
 */
@Data
public class SysUserOperateLog implements Serializable {
    private static final long serialVersionUID = 1L;

    private Integer id;
    /**
     * 会话ID
     */
    private String sessionId;
    /**
     * 租户id
     */
    private String tenantCode;
    /**
     * 用户id
     */
    private String userId;
    /**
     * 操作类型: 参考常量定义
     */
    private String type;
    /**
     * 业务ID
     */
    private String busiId;
    /**
     * 操作内容
     */
    private String content;
    /**
     * 客户端UA
     */
    private String clientUa;
    /**
     * 客户端IP
     */
    private String clientIp;
    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 首次访问时间
     */
    private Date firstAccessTime;
    /**
     * 最近访问时间
     */
    private Date lastAccessTime;
}
