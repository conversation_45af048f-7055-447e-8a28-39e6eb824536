package com.tydic.nbchat.admin.mapper;

import com.tydic.nbchat.admin.mapper.po.*;
import org.apache.ibatis.annotations.Param;


import java.util.List;

/**
 * 点赞记录表(SysUpvoteRecord)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-09-14 14:14:33
 */
public interface SysUpvoteRecordMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    SysUpvoteRecord queryById(Long id);

    List<SysUpvoteRecord> selectAll(SysUpvoteRecord sysUpvoteRecord);

    /**
     * 新增数据
     *
     * @param sysUpvoteRecord 实例对象
     * @return 影响行数
     */
    int insert(SysUpvoteRecord sysUpvoteRecord);


    int insertSelective(SysUpvoteRecord sysUpvoteRecord);

      /**
     * 修改数据
     *
     * @param sysUpvoteRecord 实例对象
     * @return 影响行数
     */
    int update(SysUpvoteRecord sysUpvoteRecord);

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return 影响行数
     */
    int deleteById(Long id);

    /**
     * 后端管理
     * @param sysUpvoteRecord
     * @return
     */
    List<SysUpvoteRecordDto> selectAudioAdminAll(SysUpvoteRecordCondition sysUpvoteRecord);
    /**
     * 后端管理
     * @param sysUpvoteRecord
     * @return
     */
    List<SysUpvoteRecordDto> selectPptAdminAll(SysUpvoteRecordCondition sysUpvoteRecord);

    /**
     * 管理员修改状态
     * @param sysUpvoteRecord
     * @return
     */
    int updateState(SysUpvoteRecord sysUpvoteRecord);

    /**
     * 查询根据级别查询
     * @param sysUpvoteRecord
     * @return
     */
    List<SysUpvoteRatingReportPO> selectCountByRating(SysUpvoteRecord sysUpvoteRecord);

    /**
     * 查询根据级别查询
     * @param sysUpvoteRecord
     * @return
     */
    List<SysUpvoteRatingReportPO> selectCountByIsLike(SysUpvoteRecord sysUpvoteRecord);
    /**
     * 根据id，产品模块 查询评价记录
     * @param id
     * @param productModule
     * @return
     */
    SysUpvoteRecord selectByPO(@Param("id") Long id, @Param("productModule") String productModule);

}

