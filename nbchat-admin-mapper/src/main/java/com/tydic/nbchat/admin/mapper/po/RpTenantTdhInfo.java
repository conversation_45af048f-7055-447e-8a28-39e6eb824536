package com.tydic.nbchat.admin.mapper.po;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025/5/28 21:04
 * @description
 */

/**
 * 数字人付费信息
 */
@Data
public class RpTenantTdhInfo implements Serializable {
    private static final long serialVersionUID = 1L;

    private Integer id;

    /**
     * 租户ID
     */
    private String tenantCode;

    /**
     * 统计日期
     */
    private Date dayData;

    /**
     * 类型 2d-照片数字人/2.5d_mtk-视频数字人/audio-声音音色
     */
    private String tdhType;

    /**
     * 定制次数
     */
    private Integer count;

    /**
     * 现金支付价格
     */
    private Integer salePrice;

    /**
     * 现金支付总金额
     */
    private Integer totalPrice;

    /**
     * 算力点支付价格
     */
    private Integer saleScore;

    /**
     * 算力点支付总金额
     */
    private Integer totalScore;

    /**
     * 免费权益次数可用次数
     */
    private Integer usable;

    /**
     * 免费权益次数总次数
     */
    private Integer allrights;

    /**
     * 更新时间
     */
    private Date updateTime;
}
