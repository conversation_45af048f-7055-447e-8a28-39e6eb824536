package com.tydic.nbchat.admin.mapper.po;

import lombok.Data;

import java.util.Date;
import java.io.Serializable;

/**
 * 点赞记录表(SysUpvoteRecord)实体类
 *
 * <AUTHOR>
 * @since 2024-09-14 14:14:33
 */
@Data
public class SysUpvoteRecord implements Serializable {
    private static final long serialVersionUID = 400359936720169008L;
/**
     * 主键
     */
    private Long id;
/**
     * 用户id
     */
    private String userId;
/**
     * 租户id
     */
    private String tenantCode;
/**
     * 业务编码
     */
    private String busiCode;
/**
     * 业务id
     */
    private String busiId;
/**
     * 点赞状态 0:踩 1:赞
     */
    private String likeStatus;
    /**
     * 点赞数
     */
    private String likesCount;
    /**
     * 点踩数
     */
    private String dislikesCount;
/**
     * 不喜欢原因: 选择
     */
    private String unlikeReason;
/**
     * 不喜欢原因: 用户输入
     */
    private String unlikeInput;
/**
     * 创建时间
     */
    private Date createTime;
    /**
     * 评分
     */
    private Integer rating;
    /**
     * 产品模块 1：视频制作 2：ppt制作 3：形象/声音定制 4：智能出题 5：其他';
     */
    private String productModule;

    /**
     * 状态: 0 待处理、1 已采纳、2 已失效、3、已审阅
     */
    private String upvoteState;

    /**
     * 处理时间
     */
    private Date handleTime;
    /**
     * 时间 (近30天)
     */
    private Integer days;

}

