package com.tydic.nbchat.admin.mapper;

/**
 * <AUTHOR>
 * @create 2025/6/9
 * @since 1.0.0
 **/

import com.tydic.nbchat.admin.mapper.po.SysShortLinkVisit;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Map;

@Mapper
public interface SysShortLinkVisitMapper {
    // 根据ID查询
    SysShortLinkVisit selectById(Long id);

    // 根据短码查询访问记录
    List<SysShortLinkVisit> selectByShortCode(String shortCode);

    // 根据用户ID查询访问记录
    List<SysShortLinkVisit> selectByUserId(String userId);

    // 插入新访问记录
    int insert(SysShortLinkVisit record);

    // 批量插入访问记录
    int insertBatch(List<SysShortLinkVisit> records);

    // 获取短码访问统计
    Map<String, Object> selectVisitStats(String shortCode);

    // 获取用户访问趋势（按天统计）
    List<Map<String, Object>> selectVisitTrend(String shortCode, int days);
}