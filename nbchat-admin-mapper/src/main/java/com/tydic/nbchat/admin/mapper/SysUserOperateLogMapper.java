package com.tydic.nbchat.admin.mapper;

import com.tydic.nbchat.admin.mapper.po.SysUserOperateLog;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/5/19 10:42
 * @description
 */

public interface SysUserOperateLogMapper {

    /**
     * insert record to table selective
     *
     * @param record the record
     * @return insert count
     */
    int insertSelective(SysUserOperateLog record);

    SysUserOperateLog selectLastBySessionId(@Param("sessionId") String sessionId);

    SysUserOperateLog selectLastByTenantCodeAndUserId(@Param("tenantCode") String tenantCode, @Param("userId") String userId);

    /**
     * 统计并分组用户操作日志记录，依据租户代码和用户ID进行分组。
     *
     * @return 按照租户代码和用户ID分组后的SysUserOperateLog列表
     */
    List<SysUserOperateLog> countGroupByTenantCodeAndUserIdSelective();

    int updateHistoryBySessionId(@Param("tenantCode") String tenantCode, @Param("userId") String userId, @Param("sessionId") String sessionId);

    int updateHistoryByTenantCodeAndUserId(@Param("tenantCode") String tenantCode, @Param("userId") String userId, @Param("sessionId") String sessionId);
}
