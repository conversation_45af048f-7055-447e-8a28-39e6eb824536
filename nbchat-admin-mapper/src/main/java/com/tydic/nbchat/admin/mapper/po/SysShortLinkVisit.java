package com.tydic.nbchat.admin.mapper.po;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @create 2025/6/9
 * @since 1.0.0
 **/
@Data
public class SysShortLinkVisit implements Serializable {
    private static final long serialVersionUID = -8807984204949111989L;
    private Long id;            // 主键ID
    private String shortCode;   // 短链唯一标识
    private String userId;      // 访问用户ID
    private Date visitTime;     // 访问时间
    private String clientIp;    // 访问IP
    private String userAgent;   // User-Agent
    private String extJson;   // 扩展信息

}
