package com.tydic.nbchat.admin.mapper;

/**
 * <AUTHOR>
 * @create 2025/6/9
 * @since 1.0.0
 **/

import com.tydic.nbchat.admin.mapper.po.SysShortLink;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

@Mapper
public interface SysShortLinkMapper {
    // 根据短码查询
    SysShortLink selectByShortCode(String shortCode);

    // 根据业务类型和ID查询 未过期的链接
    SysShortLink selectByBizTypeAndId(@Param("bizType") String bizType, @Param("bizId") String bizId);

    // 根据用户ID查询
    List<SysShortLink> selectByUserId(String userId);

    // 插入新短链接
    int insert(SysShortLink record);

    // 选择性更新（只更新非空字段）
    int updateByShortCodeSelective(SysShortLink record);

    // 更新访问时间
    int updateVisitTime(@Param("shortCode") String shortCode, @Param("visitTime") Date visitTime);

    // 删除短链接
    int deleteByShortCode(String shortCode);
}