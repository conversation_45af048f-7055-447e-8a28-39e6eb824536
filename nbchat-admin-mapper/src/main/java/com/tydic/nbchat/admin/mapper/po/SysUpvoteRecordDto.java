package com.tydic.nbchat.admin.mapper.po;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @datetime：2024/9/18 10:46
 * @description:
 */
@Data
public class SysUpvoteRecordDto implements Serializable {
    /**
     * 主键
     */
    private Long id;
    /**
     * 状态: 0 待处理、1 已采纳、2 已失效、3、已审阅
     */
    private String upvoteState;
    /**
     * 用户id
     */
    private String userId;
    /**
     * 用户名
     */
    private String userName;
    /**
     * 租户id
     */
    private String tenantCode;
    /**
     * 手机号
     */
    private String phone;
    /**
     * 企业名称
     */
    private String companyName;
    /**
     * 点赞状态 0:踩 1:赞
     */
    private String likeStatus;
    /**
     * 评分级别：1-5颗星
     */
    private Integer rating;
    /**
     * 业务编码
     */
    private String busiCode;
    /**
     * 业务id
     */
    private String busiId;

    /**
     * 不喜欢原因: 选择
     */
    private String unlikeReason;
    /**
     * 不喜欢原因: 用户输入
     */
    private String unlikeInput;

    /**
     * 反馈时间 --对应sys_upvote_record表字段createTime
     */
    private Date feedbackTime;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 产品模块1：视频制作 2：ppt制作 3：形象/声音定制 4：智能出题 5：其他
     */
    private String productModule;

    /**
     * 处理时间
     */
    private Date handleTime;
    /**
     * 排序
     */
    private String orderBy;
}
