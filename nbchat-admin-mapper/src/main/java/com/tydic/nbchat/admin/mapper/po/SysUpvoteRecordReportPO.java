package com.tydic.nbchat.admin.mapper.po;

import lombok.Data;

import java.io.Serializable;


/**
 * 评价记录原因表统计
 * sys_upvote_record_reason
 */
@Data
public class SysUpvoteRecordReportPO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 原因 选择
     */
    private String reason;

    private String reasonCode;

    /**
     * 统计数量
     */
    private Integer recordCount;

}