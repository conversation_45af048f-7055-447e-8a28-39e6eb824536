package com.tydic.nbchat.admin.mapper;

import com.tydic.nbchat.admin.mapper.po.RpUserPptVideoInfo;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/4/18 14:15
 * @description
 */

public interface RpUserPptVideoInfoMapper {

    int insertSelective(RpUserPptVideoInfo record);

    /**
     * update record selective
     *
     * @param record the updated record
     * @return update count
     */
    int updateSelective(RpUserPptVideoInfo record);

    /**
     * 根据租户代码和用户ID查找单个PPT/视频制作信息记录。
     *
     * @param tenantCode 租户代码
     * @param userId     用户ID
     * @return 匹配的RpUserPptVideoInfo对象，如果没有找到则返回null
     */
    RpUserPptVideoInfo findOneByTenantCodeAndUserId(@Param("tenantCode") String tenantCode, @Param("userId") String userId);

    int batchInsertForTimer(@Param("list") List<RpUserPptVideoInfo> list);

    /**
     * 批量更新PPT/视频制作信息记录。用于修改统计数据
     *
     * @param list 包含RpUserPptVideoInfo对象的列表，每个对象代表一条需要更新的记录
     * @return 更新操作影响的记录数
     */
    int batchUpdateForTimer(@Param("list") List<RpUserPptVideoInfo> list);

    void updateByStartTimeBetween(@Param("startTime") Date startTime, @Param("endTime") Date endTime);

    void insertByStartTimeBetween(@Param("startTime") Date startTime, @Param("endTime") Date endTime);

    List<RpUserPptVideoInfo> selectAllPptMake();

    List<RpUserPptVideoInfo> selectAllTdh();

    List<RpUserPptVideoInfo> selectAllDownload();

    int deleteAll();
}
