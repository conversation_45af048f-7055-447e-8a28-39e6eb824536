package com.tydic.nbchat.user.core.service.impl;

import com.alibaba.nacos.common.utils.CollectionUtils;
import com.alibaba.nacos.common.utils.StringUtils;
import com.alibaba.nacos.shaded.com.google.common.collect.Lists;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.tydic.nbchat.user.api.UserVideoIssuesApi;
import com.tydic.nbchat.user.api.bo.SendSmsRequest;
import com.tydic.nbchat.user.api.bo.eums.SmsHelperType;
import com.tydic.nbchat.user.api.bo.eums.SmsTemplateEnum;
import com.tydic.nbchat.user.api.bo.suggestion.UserVideoIssuesBO;
import com.tydic.nbchat.user.api.bo.suggestion.UserVideoIssuesReqBo;
import com.tydic.nbchat.user.api.bo.trade.UserBalanceRechargeReqBO;
import com.tydic.nbchat.user.api.bo.trade.UserBalanceRefundReqBO;
import com.tydic.nbchat.user.api.bo.trade.UserTradeResult;
import com.tydic.nbchat.user.api.trade.TradeBalanceApi;
import com.tydic.nbchat.user.core.enmus.SuggestionStateEnum;
import com.tydic.nbchat.user.core.utils.NbchatSmsProxyHelper;
import com.tydic.nbchat.user.mapper.UserSuggestionMapper;
import com.tydic.nbchat.user.mapper.po.UserSuggestion;
import com.tydic.nbchat.user.mapper.po.UserVideoCostResult;
import com.tydic.nbchat.user.mapper.po.UserVideoIssuesCondition;
import com.tydic.nbchat.user.mapper.po.UserVideoIssuesSelectResult;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.base.bo.RspList;
import com.tydic.nicc.dc.boot.starter.util.BaseRspUtils;
import com.tydic.nicc.framework.utils.NiccCommonUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 运营平台用户问题反馈
 */
@Slf4j
@Service
public class UserVideoIssuesServiceImpl implements UserVideoIssuesApi {
    @Resource
    private UserSuggestionMapper userSuggestionMapper;
    @Resource
    private TradeBalanceApi tradeBalanceApi;
    @Resource
    private NbchatSmsProxyHelper nbchatSmsProxyHelper;

    /**
     * 运营平台用户视频问题反馈列表查询
     * @param queryReqBO
     * @return
     */
    @Override
    public RspList<UserVideoIssuesBO> getVideoIssuesList(UserVideoIssuesReqBo queryReqBO) {
       log.info("运营平台用户视频问题反馈列表查询:{}",queryReqBO);
       UserVideoIssuesCondition condition = new UserVideoIssuesCondition();
       BeanUtils.copyProperties(queryReqBO,condition);
       Page<UserVideoIssuesSelectResult> page = PageHelper.startPage(queryReqBO.getPage(), queryReqBO.getLimit());
       List<UserVideoIssuesSelectResult> resultList = userSuggestionMapper.selectWithCreationTask(condition);
       List<UserVideoIssuesBO> list = Lists.newArrayList();
       NiccCommonUtil.copyList(resultList,list,UserVideoIssuesBO.class);
        // 添加任务时长计算逻辑
        for (UserVideoIssuesBO bo : list) {
            if (bo.getStartTime() != null && bo.getEndTime() != null) {
                bo.setDuration((bo.getEndTime().getTime() - bo.getStartTime().getTime()) / 1000); // 单位：秒
            } else if (bo.getStartTime() != null) {
                bo.setDuration((System.currentTimeMillis() - bo.getStartTime().getTime()) / 1000); // 当前时间差
            }
        }
        return BaseRspUtils.createSuccessRspList(list,page.getTotal());
    }

    /**
     * 运营平台问题反馈列表查询
     * @param queryReqBO
     * @return
     */
    @Override
    public RspList<UserVideoIssuesBO> getSuggestionList(UserVideoIssuesReqBo queryReqBO) {
        log.info("运营平台用户问题反馈列表查询:{}",queryReqBO);
        UserVideoIssuesCondition condition = new UserVideoIssuesCondition();
        BeanUtils.copyProperties(queryReqBO,condition);
        Page<UserVideoIssuesSelectResult> page = PageHelper.startPage(queryReqBO.getPage(), queryReqBO.getLimit());
        List<UserVideoIssuesSelectResult> resultList = userSuggestionMapper.selectGetSuggestionList(condition);
        List<UserVideoIssuesBO> list = Lists.newArrayList();
        NiccCommonUtil.copyList(resultList,list,UserVideoIssuesBO.class);
        return BaseRspUtils.createSuccessRspList(list,page.getTotal());
    }

    /**
     * 用户反馈视频算力点消耗查询
     * @param
     * @return
     */
    @Override
    public RspList<UserVideoIssuesBO> selectVideoCostByBizId(UserVideoIssuesReqBo queryReqBO) {
        log.info("用户反馈视频算力点消耗查询:{}",queryReqBO.getBizId());
       if(StringUtils.isEmpty(queryReqBO.getBizId())){
           return BaseRspUtils.createErrorRspList("bizId不能为空");
       }
        List<UserVideoCostResult> resultList = userSuggestionMapper.selectVideoCostByBizId(queryReqBO.getBizId());
        List<UserVideoIssuesBO> boList = Lists.newArrayList();
        NiccCommonUtil.copyList(resultList,boList,UserVideoIssuesBO.class);
        return BaseRspUtils.createSuccessRspList(boList);
    }

    /**
     * 当前算力点退还接口
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Rsp adminAuditVideoIssues(UserVideoIssuesReqBo reqBO) {
        log.info("视频问题反馈审核并退还算力点, reqBO={}", reqBO);
        UserSuggestion currentSuggestion = userSuggestionMapper.selectById(reqBO.getSuggestId());
        // 校验当前状态是否为待查看状态
        if (!SuggestionStateEnum.PENDING.getCode().equals(currentSuggestion.getSuggestionState())) {
            return BaseRspUtils.createErrorRsp("只有待查看状态可操作");
        }
        String newStatus = reqBO.getSuggestionState();
        if (SuggestionStateEnum.ACCEPTED.getCode().equals(newStatus)) {
        if (reqBO.isNeedRefund()) {
            // 仅当 needRefund == true 时进行退款校验和操作
            if (reqBO.getScore() == null || reqBO.getScore() <= 0) {
                return BaseRspUtils.createErrorRsp("退还的算力点必须大于0");
            }
            // 查询当前任务消耗的算力点
            UserVideoIssuesReqBo costReq = new UserVideoIssuesReqBo();
            costReq.setBizId(reqBO.getBizId());
            RspList<UserVideoIssuesBO> costRsp = this.selectVideoCostByBizId(costReq);
            // 确保退款积分不大于已消耗积分
            if (!costRsp.isSuccess() || CollectionUtils.isEmpty(costRsp.getRows())) {
                return BaseRspUtils.createErrorRsp("未查询到该任务的消耗记录");
            }
            Integer totalConsumedScore = costRsp.getRows().get(0).getScore();
            if (totalConsumedScore == null) {
                return BaseRspUtils.createErrorRsp("任务消耗积分数据异常");
            }
            if (reqBO.getScore() > totalConsumedScore) {
                return BaseRspUtils.createErrorRsp("退还的算力点不能大于已消耗的算力点");
            }
            // 构造退款请求
            UserBalanceRefundReqBO refundReqBO = new UserBalanceRefundReqBO();
            refundReqBO.setBizId(reqBO.getBizId());
            refundReqBO.setTradeId(reqBO.getTradeId());
            refundReqBO.setRefundScore(reqBO.getScore());
            refundReqBO.setRemark("退还用户反馈视频算力点");
            // 调用退款接口
            Rsp<UserTradeResult> refundResult = tradeBalanceApi.refund(refundReqBO);
            if (!refundResult.isSuccess()) {
                return BaseRspUtils.createErrorRsp("退款失败");
            }
            // 退款成功后发送短信通知
            this.sendRefundSuccessSms(currentSuggestion, totalConsumedScore, reqBO.getScore());
        }
        // 更新状态为“已采纳”
        UserSuggestion userSuggestion = new UserSuggestion();
        userSuggestion.setSuggestId(reqBO.getSuggestId());
        userSuggestion.setSuggestionState(SuggestionStateEnum.ACCEPTED.getCode());
        userSuggestion.setAuditTime(new Date());
        userSuggestion.setOrderNo(reqBO.getTradeId());
        int updateSuccess = userSuggestionMapper.updateSuggestion(userSuggestion);
        if (updateSuccess <= 0) {
            return BaseRspUtils.createErrorRsp("状态更新失败");
        }
        return BaseRspUtils.createSuccessRsp("处理成功");
    } else if (SuggestionStateEnum.EXPIRED.getCode().equals(newStatus)) {
        UserSuggestion userSuggestion = new UserSuggestion();
        userSuggestion.setSuggestId(reqBO.getSuggestId());
        userSuggestion.setSuggestionState(SuggestionStateEnum.EXPIRED.getCode());
        userSuggestion.setAuditTime(new Date());
        userSuggestionMapper.updateSuggestion(userSuggestion);
    }
        return BaseRspUtils.createSuccessRsp("处理成功");
    }

    /**
     * 管理员审核意见反馈赠送算力点
     * @param reqBO
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Rsp adminGiveVideoIssues(UserVideoIssuesReqBo reqBO) {
        log.info("管理员赠送视频问题反馈算力点, reqBO={}", reqBO);
        // 根据 suggestId 查询反馈信息
        UserSuggestion suggestion = userSuggestionMapper.selectById(reqBO.getSuggestId());
        if (suggestion == null) {
            return BaseRspUtils.createErrorRsp("未找到对应的反馈记录");
        }
        // 校验当前状态是否为“待采纳”
        if (!SuggestionStateEnum.PENDING.getCode().equals(suggestion.getSuggestionState())) {
            return BaseRspUtils.createErrorRsp("只有待处理状态的反馈才可以操作");
        }
        String newStatus = reqBO.getSuggestionState();
        if (SuggestionStateEnum.ACCEPTED.getCode().equals(newStatus)) {
            if (reqBO.isNeedGive()) {
                // 仅当 needGive == true 时进行赠送校验和操作
                if (reqBO.getScore() == null || reqBO.getScore() <= 0) {
                    return BaseRspUtils.createErrorRsp("赠送的算力点必须大于0");
                }
                // 构造充值请求参数
                UserBalanceRechargeReqBO rechargeReqBO = new UserBalanceRechargeReqBO();
                rechargeReqBO.setTenantCode(suggestion.getTenantCode());
                rechargeReqBO.setUserId(suggestion.getUserId());
                rechargeReqBO.setScore(reqBO.getScore());
                rechargeReqBO.setBizId(suggestion.getBusiId());
                rechargeReqBO.setExpireTime(suggestion.getExpireTime());
                rechargeReqBO.setRemark("管理员赠送用户反馈视频算力点");
                // 调用充值接口
                Rsp<UserTradeResult> rechargeResult = tradeBalanceApi.recharge(rechargeReqBO);
                if (!rechargeResult.isSuccess()) {
                    return BaseRspUtils.createErrorRsp("赠送失败：" + rechargeResult.getRspDesc());
                }
                sendGiveSuccessSms(suggestion, reqBO.getScore());
            }
            // 更新反馈状态为“已采纳”
            UserSuggestion updateParam = new UserSuggestion();
            updateParam.setSuggestId(reqBO.getSuggestId());
            updateParam.setSuggestionState(SuggestionStateEnum.ACCEPTED.getCode());
            updateParam.setAuditTime(new Date());
            int updateCount = userSuggestionMapper.updateSuggestion(updateParam);
            if (updateCount <= 0) {
                return BaseRspUtils.createErrorRsp("状态更新失败");
            }
            return BaseRspUtils.createSuccessRsp("处理成功");
        }
        if (SuggestionStateEnum.EXPIRED.getCode().equals(newStatus) ||
                SuggestionStateEnum.PROCESSED.getCode().equals(newStatus)) {
            UserSuggestion updateParam = new UserSuggestion();
            updateParam.setSuggestId(reqBO.getSuggestId());
            updateParam.setSuggestionState(newStatus);
            updateParam.setAuditTime(new Date());

            userSuggestionMapper.updateSuggestion(updateParam);
            return BaseRspUtils.createSuccessRsp("操作成功");
        }
        return BaseRspUtils.createSuccessRsp("赠送成功");
    }

    /**
     * 退还算力点成功短信通知
     * @param suggestion
     * @param totalScore
     * @param refundScore
     */
    private void sendRefundSuccessSms(UserSuggestion suggestion, Integer totalScore, Integer refundScore) {
        // 获取用户手机号
        String phone = suggestion.getPhone();
        // 计算退还比例
        double ratio = (refundScore.doubleValue() / totalScore) * 100;
        String ratioStr = String.format("%.0f", ratio);

        Map<String, String> params = new HashMap<>();
        params.put("totalScore", totalScore.toString());
        params.put("ratio", ratioStr);
        params.put("refundScore", refundScore.toString());
        // 发送短信
        SendSmsRequest smsReq = SendSmsRequest.builder()
                .templateCode(SmsTemplateEnum.VIDEO_ISSUES_AUDIT_REFUND.getTemplateID())
                .phone(phone)
                .templateParam(params)
                .build();
        Rsp smsResult = nbchatSmsProxyHelper.send(SmsHelperType.ALI, smsReq);
        if (!smsResult.isSuccess()) {
            log.error("短信发送失败，手机号：{}", smsReq.getPhone());
        }
    }
    /**
     * 赠送算力点成功短信通知
     * @param suggestion 用户反馈信息
     * @param giveScore  赠送的算力点数
     */
    private void sendGiveSuccessSms(UserSuggestion suggestion, Integer giveScore) {
        // 获取用户手机号
        String phone = suggestion.getPhone();

        Map<String, String> params = new HashMap<>();
        params.put("score", giveScore.toString());

        // 发送短信
        SendSmsRequest smsReq = SendSmsRequest.builder()
                .templateCode(SmsTemplateEnum.VIDEO_ISSUES_GIVE.getTemplateID())
                .phone(phone)
                .templateParam(params)
                .build();

        Rsp smsResult = nbchatSmsProxyHelper.send(SmsHelperType.ALI, smsReq);
        if (!smsResult.isSuccess()) {
            log.error("赠送短信发送失败，手机号：{}", smsReq.getPhone());
        }
    }
}
